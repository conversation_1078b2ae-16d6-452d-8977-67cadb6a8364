#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Module
配置管理模块
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class LLMProvider:
    """单个LLM提供商配置"""
    name: str
    api_key: str
    base_url: str
    available: bool = False

@dataclass
class LLMConfig:
    """LLM配置"""
    providers: Dict[str, LLMProvider]
    active_provider: str = ""
    models: Dict[str, str] = None
    temperature: float = 0.6
    max_tokens: int = 4000

@dataclass
class SearchConfig:
    """搜索配置"""
    tavily_api_key: str
    max_results: int = 10
    search_depth: str = "advanced"

@dataclass
class AppConfig:
    """应用配置"""
    debug: bool = False
    log_level: str = "INFO"
    max_history: int = 100
    cache_enabled: bool = True
    cache_ttl: int = 3600

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置"""
        self.config_file = config_file or "config.json"

        # 首先尝试加载.env文件
        self._load_dotenv()

        self._load_config()
        self._load_environment_variables()

        logger.info("配置加载完成")

    def _set_active_models(self, provider_id: str):
        """设置活跃提供商的模型配置"""
        if provider_id in self.provider_models:
            self.llm.models = self.provider_models[provider_id].get('models', {})
        else:
            # 使用默认模型配置
            default_models = {
                'deepseek': {
                    'reasoning': 'deepseek-reasoner',
                    'analysis': 'deepseek-chat',
                    'summary': 'deepseek-chat'
                },
                'openai': {
                    'reasoning': 'gpt-4',
                    'analysis': 'gpt-4',
                    'summary': 'gpt-3.5-turbo'
                },
                'claude': {
                    'reasoning': 'claude-3-opus-20240229',
                    'analysis': 'claude-3-sonnet-20240229',
                    'summary': 'claude-3-haiku-20240307'
                }
            }

            if provider_id in default_models:
                self.llm.models = default_models[provider_id]
            else:
                # 兼容API使用OpenAI模型名称
                self.llm.models = default_models['openai']

        logger.info(f"设置{provider_id}的模型配置: {self.llm.models}")

    def _load_dotenv(self):
        """加载.env文件"""
        env_file = ".env"
        if os.path.exists(env_file):
            try:
                # 尝试使用python-dotenv
                try:
                    from dotenv import load_dotenv
                    load_dotenv(env_file)
                    logger.info("使用python-dotenv加载.env文件")
                except ImportError:
                    # 手动加载.env文件
                    with open(env_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#') and '=' in line:
                                key, value = line.split('=', 1)
                                key = key.strip()
                                value = value.strip()
                                # 移除引号
                                if value.startswith('"') and value.endswith('"'):
                                    value = value[1:-1]
                                elif value.startswith("'") and value.endswith("'"):
                                    value = value[1:-1]
                                os.environ[key] = value
                    logger.info("手动加载.env文件")
            except Exception as e:
                logger.warning(f"加载.env文件失败: {str(e)}")
        else:
            logger.info("未找到.env文件")

    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                config_data = self._get_default_config()
                self._save_default_config(config_data)
            
            self._parse_config(config_data)
            
        except Exception as e:
            logger.warning(f"配置文件加载失败，使用默认配置: {str(e)}")
            self._parse_config(self._get_default_config())
    
    def _load_environment_variables(self):
        """加载环境变量"""
        # 加载多个LLM提供商配置
        providers = {}

        # DeepSeek
        deepseek_enabled = os.getenv('DEEPSEEK_ENABLED', 'off').lower() == 'on'
        deepseek_key = os.getenv('DEEPSEEK_API_KEY')
        if deepseek_enabled and deepseek_key:
            providers['deepseek'] = LLMProvider(
                name='DeepSeek',
                api_key=deepseek_key,
                base_url=os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com/v1'),
                available=True
            )

        # Claude
        claude_enabled = os.getenv('CLAUDE_ENABLED', 'off').lower() == 'on'
        claude_key = os.getenv('CLAUDE_API_KEY')
        if claude_enabled and claude_key:
            providers['claude'] = LLMProvider(
                name='Claude',
                api_key=claude_key,
                base_url=os.getenv('CLAUDE_BASE_URL', 'https://api.anthropic.com'),
                available=True
            )

        # OpenAI 官方
        openai_enabled = os.getenv('OPENAI_ENABLED', 'off').lower() == 'on'
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_enabled and openai_key:
            providers['openai'] = LLMProvider(
                name='OpenAI',
                api_key=openai_key,
                base_url=os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1'),
                available=True
            )

        # OpenAI 兼容 (第一个)
        compatible_enabled = os.getenv('OPENAI_COMPATIBLE_ENABLED', 'off').lower() == 'on'
        compatible_key = os.getenv('OPENAI_COMPATIBLE_API_KEY')
        if compatible_enabled and compatible_key:
            providers['openai_compatible'] = LLMProvider(
                name='OpenAI Compatible 1',
                api_key=compatible_key,
                base_url=os.getenv('OPENAI_COMPATIBLE_BASE_URL', ''),
                available=True
            )

        # OpenAI 兼容 (第二个)
        compatible_2_enabled = os.getenv('OPENAI_COMPATIBLE_2_ENABLED', 'off').lower() == 'on'
        compatible_key_2 = os.getenv('OPENAI_COMPATIBLE_API_KEY_2')
        if compatible_2_enabled and compatible_key_2:
            providers['openai_compatible_2'] = LLMProvider(
                name='OpenAI Compatible 2',
                api_key=compatible_key_2,
                base_url=os.getenv('OPENAI_COMPATIBLE_BASE_URL_2', ''),
                available=True
            )

        # 更新LLM配置
        self.llm.providers = providers

        # 选择第一个可用的提供商作为活跃提供商
        if providers:
            self.llm.active_provider = list(providers.keys())[0]
            # 设置对应的模型配置
            self._set_active_models(self.llm.active_provider)

        # 搜索配置
        tavily_enabled = os.getenv('TAVILY_ENABLED', 'off').lower() == 'on'
        tavily_key = os.getenv('TAVILY_API_KEY')
        if tavily_enabled and tavily_key:
            self.search.tavily_api_key = tavily_key

        # 应用配置
        if os.getenv('DEBUG'):
            self.app.debug = os.getenv('DEBUG').lower() == 'true'

        if os.getenv('LOG_LEVEL'):
            self.app.log_level = os.getenv('LOG_LEVEL')
    
    def _parse_config(self, config_data: Dict[str, Any]):
        """解析配置数据"""
        # LLM配置
        llm_config = config_data.get('llm', {})

        # 存储每个提供商的模型配置
        self.provider_models = llm_config.get('providers', {})

        self.llm = LLMConfig(
            providers={},  # 将在环境变量加载时填充
            active_provider="",
            models={},  # 将根据活跃提供商动态设置
            temperature=llm_config.get('temperature', 0.6),
            max_tokens=llm_config.get('max_tokens', 4000)
        )
        
        # 搜索配置
        search_config = config_data.get('search', {})
        self.search = SearchConfig(
            tavily_api_key=search_config.get('tavily_api_key', ''),
            max_results=search_config.get('max_results', 10),
            search_depth=search_config.get('search_depth', 'advanced')
        )
        
        # 应用配置
        app_config = config_data.get('app', {})
        self.app = AppConfig(
            debug=app_config.get('debug', False),
            log_level=app_config.get('log_level', 'INFO'),
            max_history=app_config.get('max_history', 100),
            cache_enabled=app_config.get('cache_enabled', True),
            cache_ttl=app_config.get('cache_ttl', 3600)
        )
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "llm": {
                "providers": {},
                "models": {
                    "reasoning": "deepseek-ai/DeepSeek-R1",
                    "analysis": "Qwen/Qwen2.5-72B-Instruct",
                    "summary": "Qwen/Qwen2.5-72B-Instruct"
                },
                "temperature": 0.6,
                "max_tokens": 4000
            },
            "search": {
                "tavily_api_key": "",
                "max_results": 10,
                "search_depth": "advanced"
            },
            "app": {
                "debug": False,
                "log_level": "INFO",
                "max_history": 100,
                "cache_enabled": True,
                "cache_ttl": 3600
            },
            "ui": {
                "default_language": "zh",
                "theme": "soft",
                "max_chat_history": 50
            },
            "analysis": {
                "max_competitors": 10,
                "max_search_queries": 5,
                "confidence_threshold": 0.7
            }
        }
    
    def _save_default_config(self, config_data: Dict[str, Any]):
        """保存默认配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"默认配置已保存到 {self.config_file}")
        except Exception as e:
            logger.warning(f"保存默认配置失败: {str(e)}")
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                return config_data.get('ui', {})
        except Exception:
            pass
        
        return {
            "default_language": "zh",
            "theme": "soft",
            "max_chat_history": 50
        }
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """获取分析配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                return config_data.get('analysis', {})
        except Exception:
            pass
        
        return {
            "max_competitors": 10,
            "max_search_queries": 5,
            "confidence_threshold": 0.7
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性，返回验证结果"""
        warnings = []
        errors = []

        # 检查LLM提供商
        if not self.llm.providers:
            warnings.append("未启用任何LLM API，AI功能将不可用")
        else:
            available_providers = [p.name for p in self.llm.providers.values() if p.available]
            logger.info(f"可用的LLM提供商: {', '.join(available_providers)}")

        # 检查搜索配置
        if not self.search.tavily_api_key:
            warnings.append("未启用Tavily API，搜索功能将不可用")

        # 检查模型配置
        required_models = ['reasoning', 'analysis', 'summary']
        for model_type in required_models:
            if model_type not in self.llm.models:
                errors.append(f"缺少{model_type}模型配置")

        # 记录结果
        if errors:
            logger.error("配置验证发现错误:")
            for error in errors:
                logger.error(f"  - {error}")

        if warnings:
            logger.warning("配置验证发现警告:")
            for warning in warnings:
                logger.warning(f"  - {warning}")

        if not errors and not warnings:
            logger.info("配置验证通过")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "has_llm": len(self.llm.providers) > 0,
            "has_search": bool(self.search.tavily_api_key)
        }
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        try:
            # 读取当前配置
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                config_data = self._get_default_config()
            
            # 递归更新配置
            def deep_update(base_dict, update_dict):
                for key, value in update_dict.items():
                    if isinstance(value, dict) and key in base_dict:
                        deep_update(base_dict[key], value)
                    else:
                        base_dict[key] = value
            
            deep_update(config_data, updates)
            
            # 保存更新后的配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            # 重新加载配置
            self._parse_config(config_data)
            
            logger.info("配置更新成功")
            return True
            
        except Exception as e:
            logger.error(f"配置更新失败: {str(e)}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "llm": {
                "api_key_configured": bool(self.llm.api_key),
                "base_url": self.llm.base_url,
                "models": list(self.llm.models.keys()),
                "temperature": self.llm.temperature
            },
            "search": {
                "tavily_configured": bool(self.search.tavily_api_key),
                "max_results": self.search.max_results,
                "search_depth": self.search.search_depth
            },
            "app": {
                "debug": self.app.debug,
                "log_level": self.app.log_level,
                "cache_enabled": self.app.cache_enabled
            }
        }
