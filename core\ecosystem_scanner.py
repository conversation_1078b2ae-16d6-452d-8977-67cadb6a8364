#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ecosystem Scanner Module
生态扫描与竞品分析引擎 - 负责搜索和分析市场生态
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class CompetitorInfo:
    """竞品信息"""
    name: str
    description: str
    url: str
    category: str
    features: List[str]
    pricing: str
    user_count: str
    rating: float
    pros: List[str]
    cons: List[str]
    last_updated: str

class EcosystemScanner:
    """生态扫描与竞品分析引擎"""
    
    def __init__(self, config):
        """初始化生态扫描器"""
        self.config = config
        
        # 搜索源配置
        self.search_sources = {
            "web": True,
            "product_hunt": True,
            "github": True,
            "app_stores": True
        }
        
        logger.info("生态扫描器初始化完成")
    
    async def scan(
        self, 
        demand_analysis: Dict[str, Any], 
        language: str = "zh"
    ) -> Dict[str, Any]:
        """
        执行生态扫描
        
        Args:
            demand_analysis: 需求分析结果
            language: 语言
            
        Returns:
            生态扫描结果字典
        """
        try:
            logger.info("开始生态扫描...")
            
            # 获取LLM接口（从全局应用实例）
            from app import get_app_instance
            app = get_app_instance()
            llm = app.llm_interface
            
            # 执行多源搜索
            search_results = await self._perform_multi_source_search(
                demand_analysis, llm
            )
            
            # 分析竞品
            competitors = await self._analyze_competitors(
                search_results, demand_analysis, llm, language
            )
            
            # 评估市场成熟度
            market_maturity = self._assess_market_maturity(competitors, search_results)
            
            # 识别市场空白
            market_gaps = await self._identify_market_gaps(
                competitors, demand_analysis, llm, language
            )
            
            # 构建扫描结果
            scan_result = {
                "search_results": search_results,
                "competitors": competitors,
                "market_maturity": market_maturity,
                "market_gaps": market_gaps,
                "total_competitors": len(competitors),
                "scan_timestamp": self._get_timestamp(),
                "search_queries_used": demand_analysis.get("search_queries", []),
                "category": demand_analysis.get("category", "未分类")
            }
            
            logger.info(f"生态扫描完成，发现 {len(competitors)} 个竞品")
            return scan_result
            
        except Exception as e:
            logger.error(f"生态扫描失败: {str(e)}")
            return self._create_fallback_scan_result(demand_analysis)
    
    async def _perform_multi_source_search(
        self, 
        demand_analysis: Dict[str, Any], 
        llm
    ) -> List[Dict[str, Any]]:
        """执行多源搜索"""
        all_results = []
        search_queries = demand_analysis.get("search_queries", [])
        
        for query in search_queries[:3]:  # 限制搜索查询数量
            try:
                # 使用Tavily搜索
                search_result = await llm.search_web(
                    query=query,
                    search_depth="advanced",
                    max_results=8
                )
                
                for result in search_result.get("results", []):
                    all_results.append({
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "content": result.get("content", ""),
                        "score": result.get("score", 0.0),
                        "source": "web_search",
                        "query": query,
                        "published_date": result.get("published_date", "")
                    })
                
                # 添加延迟避免频率限制
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.warning(f"搜索查询 '{query}' 失败: {str(e)}")
                continue
        
        # 去重和排序
        unique_results = self._deduplicate_results(all_results)
        return sorted(unique_results, key=lambda x: x.get("score", 0), reverse=True)[:20]
    
    async def _analyze_competitors(
        self, 
        search_results: List[Dict[str, Any]], 
        demand_analysis: Dict[str, Any], 
        llm,
        language: str
    ) -> List[Dict[str, Any]]:
        """分析竞品信息"""
        competitors = []
        
        # 构建竞品分析提示词
        analysis_prompt = self._build_competitor_analysis_prompt(
            search_results, demand_analysis, language
        )
        
        try:
            # 调用LLM分析竞品
            response = await llm.generate_analysis(analysis_prompt)
            analysis_result = await llm.parse_json_response(response.content)
            
            # 提取竞品信息
            competitors_data = analysis_result.get("competitors", [])
            
            for comp_data in competitors_data[:10]:  # 限制竞品数量
                competitor = {
                    "name": comp_data.get("name", "未知产品"),
                    "description": comp_data.get("description", ""),
                    "url": comp_data.get("url", ""),
                    "category": comp_data.get("category", "未分类"),
                    "features": comp_data.get("features", []),
                    "pricing": comp_data.get("pricing", "未知"),
                    "user_count": comp_data.get("user_count", "未知"),
                    "rating": float(comp_data.get("rating", 0.0)),
                    "pros": comp_data.get("pros", []),
                    "cons": comp_data.get("cons", []),
                    "market_position": comp_data.get("market_position", "未知"),
                    "target_users": comp_data.get("target_users", []),
                    "last_updated": comp_data.get("last_updated", ""),
                    "relevance_score": float(comp_data.get("relevance_score", 0.5))
                }
                competitors.append(competitor)
            
        except Exception as e:
            logger.warning(f"竞品分析失败，使用基础分析: {str(e)}")
            # 基础竞品提取
            competitors = self._extract_basic_competitors(search_results)
        
        return competitors
    
    def _build_competitor_analysis_prompt(
        self, 
        search_results: List[Dict[str, Any]], 
        demand_analysis: Dict[str, Any], 
        language: str
    ) -> str:
        """构建竞品分析提示词"""
        
        # 准备搜索结果摘要
        results_summary = ""
        for i, result in enumerate(search_results[:10], 1):
            results_summary += f"{i}. {result.get('title', '')}\n"
            results_summary += f"   URL: {result.get('url', '')}\n"
            results_summary += f"   内容: {result.get('content', '')[:200]}...\n\n"
        
        if language == "zh":
            prompt = f"""作为市场分析专家，请基于以下搜索结果分析竞品信息。

用户需求：{demand_analysis.get('summary', '')}
需求关键词：{', '.join(demand_analysis.get('keywords', []))}

搜索结果：
{results_summary}

请按照以下JSON格式返回竞品分析：

{{
    "competitors": [
        {{
            "name": "产品名称",
            "description": "产品描述",
            "url": "产品网址",
            "category": "产品分类",
            "features": ["主要功能特性"],
            "pricing": "价格信息",
            "user_count": "用户规模",
            "rating": 4.5,
            "pros": ["优势"],
            "cons": ["劣势"],
            "market_position": "市场定位",
            "target_users": ["目标用户群体"],
            "last_updated": "最后更新时间",
            "relevance_score": 0.9
        }}
    ],
    "market_overview": "市场概况总结",
    "competition_intensity": "竞争激烈程度（低/中/高）"
}}

分析要求：
1. 识别与用户需求最相关的产品和服务
2. 提取关键的产品信息和特性
3. 评估每个竞品与用户需求的相关性
4. 分析市场竞争格局
5. 确保信息准确性和时效性

请返回有效的JSON格式。"""

        else:  # English
            prompt = f"""As a market analysis expert, please analyze competitor information based on the following search results.

User requirement: {demand_analysis.get('summary', '')}
Requirement keywords: {', '.join(demand_analysis.get('keywords', []))}

Search results:
{results_summary}

Please return competitor analysis in the following JSON format:

{{
    "competitors": [
        {{
            "name": "Product name",
            "description": "Product description",
            "url": "Product URL",
            "category": "Product category",
            "features": ["Main features"],
            "pricing": "Pricing information",
            "user_count": "User scale",
            "rating": 4.5,
            "pros": ["Advantages"],
            "cons": ["Disadvantages"],
            "market_position": "Market positioning",
            "target_users": ["Target user groups"],
            "last_updated": "Last update time",
            "relevance_score": 0.9
        }}
    ],
    "market_overview": "Market overview summary",
    "competition_intensity": "Competition intensity (low/medium/high)"
}}

Analysis requirements:
1. Identify products and services most relevant to user needs
2. Extract key product information and features
3. Evaluate relevance of each competitor to user needs
4. Analyze market competition landscape
5. Ensure information accuracy and timeliness

Please return valid JSON format."""
        
        return prompt
    
    def _assess_market_maturity(
        self, 
        competitors: List[Dict[str, Any]], 
        search_results: List[Dict[str, Any]]
    ) -> float:
        """评估市场成熟度"""
        if not competitors:
            return 0.1
        
        # 基于竞品数量、用户规模、产品成熟度等因素评估
        competitor_count = len(competitors)
        
        # 竞品数量评分
        if competitor_count >= 10:
            count_score = 1.0
        elif competitor_count >= 5:
            count_score = 0.7
        elif competitor_count >= 2:
            count_score = 0.4
        else:
            count_score = 0.1
        
        # 产品评分平均值
        ratings = [comp.get("rating", 0) for comp in competitors if comp.get("rating", 0) > 0]
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        rating_score = avg_rating / 5.0
        
        # 综合评分
        maturity_score = (count_score * 0.6 + rating_score * 0.4)
        return min(1.0, max(0.0, maturity_score))
    
    async def _identify_market_gaps(
        self, 
        competitors: List[Dict[str, Any]], 
        demand_analysis: Dict[str, Any], 
        llm,
        language: str
    ) -> List[Dict[str, Any]]:
        """识别市场空白"""
        try:
            gap_analysis_prompt = self._build_gap_analysis_prompt(
                competitors, demand_analysis, language
            )
            
            response = await llm.generate_analysis(gap_analysis_prompt)
            analysis_result = await llm.parse_json_response(response.content)
            
            return analysis_result.get("market_gaps", [])
            
        except Exception as e:
            logger.warning(f"市场空白分析失败: {str(e)}")
            return []
    
    def _build_gap_analysis_prompt(
        self, 
        competitors: List[Dict[str, Any]], 
        demand_analysis: Dict[str, Any], 
        language: str
    ) -> str:
        """构建市场空白分析提示词"""
        
        competitors_summary = ""
        for comp in competitors[:5]:
            competitors_summary += f"- {comp.get('name', '')}: {comp.get('description', '')}\n"
            competitors_summary += f"  特性: {', '.join(comp.get('features', [])[:3])}\n"
        
        if language == "zh":
            return f"""基于以下竞品分析，识别市场空白和机会：

用户需求：{demand_analysis.get('summary', '')}
主要竞品：
{competitors_summary}

请按JSON格式返回市场空白分析：

{{
    "market_gaps": [
        {{
            "gap_type": "功能空白/用户群空白/价格空白等",
            "description": "空白描述",
            "opportunity_level": "高/中/低",
            "potential_solutions": ["可能的解决方案"]
        }}
    ]
}}"""
        
        else:
            return f"""Based on the following competitor analysis, identify market gaps and opportunities:

User requirement: {demand_analysis.get('summary', '')}
Main competitors:
{competitors_summary}

Please return market gap analysis in JSON format:

{{
    "market_gaps": [
        {{
            "gap_type": "Feature gap/User group gap/Price gap etc.",
            "description": "Gap description",
            "opportunity_level": "High/Medium/Low",
            "potential_solutions": ["Potential solutions"]
        }}
    ]
}}"""
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重搜索结果"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            url = result.get("url", "")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        return unique_results
    
    def _extract_basic_competitors(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基础竞品提取"""
        competitors = []
        
        for result in search_results[:5]:
            competitor = {
                "name": result.get("title", "未知产品"),
                "description": result.get("content", "")[:200],
                "url": result.get("url", ""),
                "category": "未分类",
                "features": [],
                "pricing": "未知",
                "user_count": "未知",
                "rating": 0.0,
                "pros": [],
                "cons": [],
                "market_position": "未知",
                "target_users": [],
                "last_updated": "",
                "relevance_score": result.get("score", 0.5)
            }
            competitors.append(competitor)
        
        return competitors
    
    def _create_fallback_scan_result(self, demand_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """创建备用扫描结果"""
        return {
            "search_results": [],
            "competitors": [],
            "market_maturity": 0.3,
            "market_gaps": [],
            "total_competitors": 0,
            "scan_timestamp": self._get_timestamp(),
            "search_queries_used": demand_analysis.get("search_queries", []),
            "category": demand_analysis.get("category", "未分类"),
            "fallback": True,
            "error": "生态扫描失败，返回基础结果"
        }
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
