#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM Interface Module
统一的大语言模型接口层，兼容多种模型
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import logging

from openai import OpenAI
from tavily import TavilyClient

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """LLM响应数据类"""
    content: str
    model: str
    usage: Dict[str, Any]
    metadata: Dict[str, Any] = None

class LLMInterface:
    """统一的LLM接口类"""
    
    def __init__(self, config):
        """初始化LLM接口"""
        self.config = config
        self.clients = {}
        self.active_client = None

        # 初始化所有可用的客户端
        for provider_id, provider in config.llm.providers.items():
            if provider.available:
                try:
                    if provider_id == 'claude':
                        # Claude使用不同的客户端
                        # 这里可以添加Anthropic客户端初始化
                        logger.info(f"Claude客户端暂不支持，跳过")
                        continue
                    else:
                        # 其他提供商使用OpenAI兼容接口
                        client = OpenAI(
                            api_key=provider.api_key,
                            base_url=provider.base_url if provider.base_url else None
                        )
                        self.clients[provider_id] = {
                            'client': client,
                            'provider': provider
                        }

                        # 设置第一个可用客户端为活跃客户端
                        if not self.active_client:
                            self.active_client = provider_id

                        logger.info(f"初始化{provider.name}客户端成功")

                except Exception as e:
                    logger.warning(f"初始化{provider.name}客户端失败: {str(e)}")

        if not self.clients:
            logger.warning("未找到可用的LLM客户端，AI功能将不可用")
            self.active_client = None
        
        # 初始化Tavily客户端
        tavily_key = os.getenv('TAVILY_API_KEY') or config.search.tavily_api_key
        if not tavily_key:
            logger.warning("Tavily API密钥未设置，搜索功能将不可用")
            self.tavily_client = None
        else:
            try:
                self.tavily_client = TavilyClient(api_key=tavily_key)
            except Exception as e:
                logger.warning(f"Tavily客户端初始化失败: {str(e)}")
                self.tavily_client = None
        
        # 模型配置 - 从配置中获取
        self.models = config.llm.models
        
        logger.info("LLM接口初始化完成")

    def switch_provider(self, provider_id: str) -> bool:
        """
        切换AI提供商

        Args:
            provider_id: 提供商ID

        Returns:
            bool: 切换是否成功
        """
        if provider_id == "none" or provider_id not in self.clients:
            logger.warning(f"无法切换到提供商: {provider_id}")
            return False

        old_provider = self.active_client
        self.active_client = provider_id

        # 更新模型配置
        self.config._set_active_models(provider_id)
        self.models = self.config.llm.models

        provider_name = self.clients[provider_id]['provider'].name
        logger.info(f"AI提供商已切换: {old_provider} -> {provider_id} ({provider_name})")
        logger.info(f"模型配置已更新: {self.models}")
        return True

    def get_current_provider_info(self) -> Dict[str, str]:
        """获取当前提供商信息"""
        if not self.active_client or self.active_client not in self.clients:
            return {
                "id": "none",
                "name": "未配置",
                "status": "不可用"
            }

        provider = self.clients[self.active_client]['provider']
        return {
            "id": self.active_client,
            "name": provider.name,
            "status": "可用"
        }
    
    async def generate_reasoning(
        self,
        prompt: str,
        context: List[Dict[str, str]] = None,
        temperature: float = 0.6
    ) -> LLMResponse:
        """
        使用推理模型生成思考过程

        Args:
            prompt: 输入提示
            context: 上下文消息
            temperature: 温度参数

        Returns:
            LLMResponse: 模型响应
        """
        if not self.active_client:
            return LLMResponse(
                content="AI功能不可用：未配置有效的API密钥。请在.env文件中配置DEEPSEEK_API_KEY、OPENAI_API_KEY等密钥。",
                model="unavailable",
                usage={},
                metadata={"type": "error", "reason": "no_api_key"}
            )

        try:
            messages = context or []
            messages.append({"role": "user", "content": prompt})

            client_info = self.clients[self.active_client]
            response = client_info['client'].chat.completions.create(
                model=self.models["reasoning"],
                messages=messages,
                temperature=temperature,
                stream=False
            )

            return LLMResponse(
                content=response.choices[0].message.content,
                model=self.models["reasoning"],
                usage=response.usage.model_dump() if response.usage else {},
                metadata={"type": "reasoning", "provider": client_info['provider'].name}
            )

        except Exception as e:
            logger.error(f"推理生成失败: {str(e)}")
            return LLMResponse(
                content=f"AI服务暂时不可用: {str(e)}",
                model="error",
                usage={},
                metadata={"type": "error", "reason": str(e)}
            )
    
    async def generate_analysis(
        self,
        prompt: str,
        context: List[Dict[str, str]] = None,
        temperature: float = 0.3
    ) -> LLMResponse:
        """
        使用分析模型生成结构化分析

        Args:
            prompt: 输入提示
            context: 上下文消息
            temperature: 温度参数

        Returns:
            LLMResponse: 模型响应
        """
        if not self.active_client:
            return LLMResponse(
                content="AI功能不可用：未配置有效的API密钥。请在.env文件中配置DEEPSEEK_API_KEY、OPENAI_API_KEY等密钥。",
                model="unavailable",
                usage={},
                metadata={"type": "error", "reason": "no_api_key"}
            )

        try:
            messages = context or []
            messages.append({"role": "user", "content": prompt})

            client_info = self.clients[self.active_client]
            response = client_info['client'].chat.completions.create(
                model=self.models["analysis"],
                messages=messages,
                temperature=temperature,
                stream=False
            )

            return LLMResponse(
                content=response.choices[0].message.content,
                model=self.models["analysis"],
                usage=response.usage.model_dump() if response.usage else {},
                metadata={"type": "analysis", "provider": client_info['provider'].name}
            )

        except Exception as e:
            logger.error(f"分析生成失败: {str(e)}")
            return LLMResponse(
                content=f"AI服务暂时不可用: {str(e)}",
                model="error",
                usage={},
                metadata={"type": "error", "reason": str(e)}
            )
    
    async def generate_summary(
        self, 
        content: str, 
        summary_type: str = "general",
        language: str = "zh"
    ) -> LLMResponse:
        """
        生成内容摘要
        
        Args:
            content: 要总结的内容
            summary_type: 摘要类型
            language: 目标语言
            
        Returns:
            LLMResponse: 模型响应
        """
        try:
            prompt = self._build_summary_prompt(content, summary_type, language)
            
            response = self.openai_client.chat.completions.create(
                model=self.models["summary"],
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                stream=False
            )
            
            return LLMResponse(
                content=response.choices[0].message.content,
                model=self.models["summary"],
                usage=response.usage.model_dump() if response.usage else {},
                metadata={"type": "summary", "summary_type": summary_type}
            )
            
        except Exception as e:
            logger.error(f"摘要生成失败: {str(e)}")
            raise
    
    async def search_web(
        self,
        query: str,
        search_depth: str = "advanced",
        max_results: int = 10
    ) -> Dict[str, Any]:
        """
        使用Tavily进行网络搜索

        Args:
            query: 搜索查询
            search_depth: 搜索深度
            max_results: 最大结果数

        Returns:
            搜索结果字典
        """
        if not self.tavily_client:
            logger.warning("Tavily客户端未初始化，返回空搜索结果")
            return {
                "query": query,
                "results": [],
                "total_results": 0,
                "error": "搜索服务不可用"
            }

        try:
            results = self.tavily_client.search(
                query=query,
                search_depth=search_depth,
                max_results=max_results
            )
            
            # 格式化搜索结果
            formatted_results = []
            for result in results.get("results", []):
                formatted_results.append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0),
                    "published_date": result.get("published_date", "")
                })
            
            return {
                "query": query,
                "results": formatted_results,
                "total_results": len(formatted_results),
                "search_metadata": {
                    "depth": search_depth,
                    "timestamp": self._get_timestamp()
                }
            }
            
        except Exception as e:
            logger.error(f"网络搜索失败: {str(e)}")
            return {
                "query": query,
                "results": [],
                "total_results": 0,
                "error": str(e)
            }
    
    def _build_summary_prompt(self, content: str, summary_type: str, language: str) -> str:
        """构建摘要提示词"""
        if language == "zh":
            if summary_type == "competitive":
                return f"""请对以下竞品分析内容进行总结，重点关注：
1. 主要竞品及其特点
2. 市场格局分析
3. 竞争优势对比
4. 市场机会识别

内容：
{content}

请用中文提供简洁明了的总结。"""
            
            elif summary_type == "technical":
                return f"""请对以下技术可行性分析进行总结，重点关注：
1. 技术难度评估
2. 所需技术栈
3. 开发周期估算
4. 技术风险分析

内容：
{content}

请用中文提供专业的技术总结。"""
            
            else:
                return f"""请对以下内容进行总结，提取关键信息和要点：

{content}

请用中文提供清晰简洁的总结。"""
        
        else:  # English
            if summary_type == "competitive":
                return f"""Please summarize the following competitive analysis, focusing on:
1. Main competitors and their features
2. Market landscape analysis
3. Competitive advantage comparison
4. Market opportunity identification

Content:
{content}

Please provide a concise summary in English."""
            
            elif summary_type == "technical":
                return f"""Please summarize the following technical feasibility analysis, focusing on:
1. Technical difficulty assessment
2. Required technology stack
3. Development timeline estimation
4. Technical risk analysis

Content:
{content}

Please provide a professional technical summary in English."""
            
            else:
                return f"""Please summarize the following content, extracting key information and main points:

{content}

Please provide a clear and concise summary in English."""
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    async def parse_json_response(self, response: str) -> Dict[str, Any]:
        """解析JSON格式的响应"""
        try:
            # 尝试直接解析
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果失败，尝试提取JSON部分
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass
            
            # 如果仍然失败，返回错误信息
            logger.warning(f"无法解析JSON响应: {response[:200]}...")
            return {"error": "JSON解析失败", "raw_response": response}
