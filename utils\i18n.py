#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Internationalization Module
国际化支持模块
"""

import json
import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class I18nManager:
    """国际化管理器"""
    
    def __init__(self, locale_dir: str = "locales"):
        """初始化国际化管理器"""
        self.locale_dir = locale_dir
        self.translations = {}
        self.default_language = "zh"
        self.supported_languages = ["zh", "en"]
        
        self._load_translations()
        logger.info("国际化管理器初始化完成")
    
    def _load_translations(self):
        """加载翻译文件"""
        # 如果locale目录不存在，创建默认翻译
        if not os.path.exists(self.locale_dir):
            os.makedirs(self.locale_dir, exist_ok=True)
            self._create_default_translations()
        
        # 加载所有语言的翻译文件
        for lang in self.supported_languages:
            lang_file = os.path.join(self.locale_dir, f"{lang}.json")
            try:
                if os.path.exists(lang_file):
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        self.translations[lang] = json.load(f)
                else:
                    self.translations[lang] = self._get_default_translation(lang)
                    self._save_translation(lang, self.translations[lang])
                
                logger.info(f"加载语言包: {lang}")
                
            except Exception as e:
                logger.warning(f"加载语言包 {lang} 失败: {str(e)}")
                self.translations[lang] = self._get_default_translation(lang)
    
    def _create_default_translations(self):
        """创建默认翻译文件"""
        for lang in self.supported_languages:
            translation = self._get_default_translation(lang)
            self._save_translation(lang, translation)
    
    def _get_default_translation(self, language: str) -> Dict[str, str]:
        """获取默认翻译"""
        if language == "zh":
            return {
                # 通用
                "app_title": "逛逛 (InsightPulse)",
                "app_subtitle": "需求探索与可行性分析平台",
                "app_description": "连接潜在用户需求与可行的解决方案，赋能创新者、开发者及普通用户",
                
                # 用户模式
                "user_mode": "用户模式",
                "regular_mode": "🔍 普通用户模式 - 寻找解决方案",
                "developer_mode": "🚀 开发者模式 - 探索创新机会",
                "regular_mode_desc": "为您推荐现有的解决方案和产品",
                "developer_mode_desc": "分析市场机会、竞品情况和技术可行性",
                
                # 界面元素
                "language": "语言",
                "analyze_button": "🔍 分析",
                "clear_history": "🗑️ 清除历史",
                "input_placeholder": "请描述您的需求，例如：有什么好用的笔记软件？",
                "result_placeholder": "在这里查看分析结果...",
                
                # 示例问题
                "example_note_software": "📝 有什么好用的笔记软件？",
                "example_fitness_app": "🏃‍♂️ 推荐一些健身APP",
                "example_ai_writing": "💡 AI写作助手的市场机会",
                "example_task_manager": "🔧 开发一个任务管理工具",
                
                # 功能说明
                "core_features": "🌟 核心功能",
                "feature_demand_analysis": "🔍 智能需求解析",
                "feature_ecosystem_scan": "🌐 生态扫描分析",
                "feature_competitor_analysis": "📊 竞品对比评估",
                "feature_innovation_radar": "💡 创新机会识别",
                "feature_feasibility_score": "⚡ 可行性评分",
                "feature_decision_support": "📋 决策建议生成",
                
                # 分析过程
                "analyzing": "正在分析",
                "analysis_complete": "分析完成",
                "analysis_error": "分析出错",
                
                # 结果展示
                "solution_recommendations": "解决方案推荐",
                "innovation_analysis": "创新机会分析",
                "demand_understanding": "需求理解",
                "feasibility_assessment": "📊 可行性评估",
                "confidence_level": "置信度",
                "market_opportunity": "💡 市场机会",
                "technical_feasibility": "⚙️ 技术可行性",
                "main_competitors": "🏢 主要竞品",
                "reference_materials": "📚 参考资料",
                
                # 评估等级
                "high": "高",
                "medium": "中",
                "low": "低",
                "unknown": "未知",
                
                # 状态信息
                "tip_select_mode": "💡 提示：选择用户模式后，输入您的需求开始分析",
                "history_cleared": "✅ 历史记录已清除",
                "export_coming_soon": "分析报告导出功能即将上线",
                
                # 错误信息
                "input_required": "请输入您的需求描述",
                "analysis_failed": "分析失败，请重试",
                "network_error": "网络连接错误",
                "api_error": "API调用失败",
                
                # 其他
                "learn_more": "🔗 了解更多",
                "score": "评分",
                "category": "分类",
                "users": "用户",
                "uncategorized": "未分类"
            }
        
        else:  # English
            return {
                # General
                "app_title": "InsightPulse",
                "app_subtitle": "Demand Exploration and Feasibility Analysis Platform",
                "app_description": "Connecting potential user needs with viable solutions, empowering innovators, developers and regular users",
                
                # User modes
                "user_mode": "User Mode",
                "regular_mode": "🔍 Regular User Mode - Find Solutions",
                "developer_mode": "🚀 Developer Mode - Explore Innovation Opportunities",
                "regular_mode_desc": "Recommend existing solutions and products for you",
                "developer_mode_desc": "Analyze market opportunities, competitors and technical feasibility",
                
                # UI elements
                "language": "Language",
                "analyze_button": "🔍 Analyze",
                "clear_history": "🗑️ Clear History",
                "input_placeholder": "Please describe your needs, e.g.: What are some good note-taking software?",
                "result_placeholder": "View analysis results here...",
                
                # Example questions
                "example_note_software": "📝 What are some good note-taking software?",
                "example_fitness_app": "🏃‍♂️ Recommend some fitness apps",
                "example_ai_writing": "💡 Market opportunities for AI writing assistants",
                "example_task_manager": "🔧 Develop a task management tool",
                
                # Features
                "core_features": "🌟 Core Features",
                "feature_demand_analysis": "🔍 Intelligent Demand Analysis",
                "feature_ecosystem_scan": "🌐 Ecosystem Scanning",
                "feature_competitor_analysis": "📊 Competitive Analysis",
                "feature_innovation_radar": "💡 Innovation Opportunity Identification",
                "feature_feasibility_score": "⚡ Feasibility Scoring",
                "feature_decision_support": "📋 Decision Support Generation",
                
                # Analysis process
                "analyzing": "Analyzing",
                "analysis_complete": "Analysis Complete",
                "analysis_error": "Analysis Error",
                
                # Results
                "solution_recommendations": "Solution Recommendations",
                "innovation_analysis": "Innovation Opportunity Analysis",
                "demand_understanding": "Demand Understanding",
                "feasibility_assessment": "📊 Feasibility Assessment",
                "confidence_level": "Confidence Level",
                "market_opportunity": "💡 Market Opportunity",
                "technical_feasibility": "⚙️ Technical Feasibility",
                "main_competitors": "🏢 Main Competitors",
                "reference_materials": "📚 Reference Materials",
                
                # Assessment levels
                "high": "High",
                "medium": "Medium",
                "low": "Low",
                "unknown": "Unknown",
                
                # Status messages
                "tip_select_mode": "💡 Tip: Select user mode and input your requirements to start analysis",
                "history_cleared": "✅ History cleared",
                "export_coming_soon": "Analysis report export feature coming soon",
                
                # Error messages
                "input_required": "Please enter your requirement description",
                "analysis_failed": "Analysis failed, please try again",
                "network_error": "Network connection error",
                "api_error": "API call failed",
                
                # Others
                "learn_more": "🔗 Learn More",
                "score": "Score",
                "category": "Category",
                "users": "Users",
                "uncategorized": "Uncategorized"
            }
    
    def _save_translation(self, language: str, translation: Dict[str, str]):
        """保存翻译文件"""
        try:
            lang_file = os.path.join(self.locale_dir, f"{language}.json")
            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump(translation, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"保存翻译文件 {language} 失败: {str(e)}")
    
    def get_text(self, key: str, language: str = None) -> str:
        """获取翻译文本"""
        if language is None:
            language = self.default_language
        
        if language not in self.supported_languages:
            language = self.default_language
        
        if language in self.translations:
            return self.translations[language].get(key, key)
        
        return key
    
    def get_all_texts(self, language: str = None) -> Dict[str, str]:
        """获取所有翻译文本"""
        if language is None:
            language = self.default_language
        
        if language not in self.supported_languages:
            language = self.default_language
        
        return self.translations.get(language, {})
    
    def add_translation(self, key: str, translations: Dict[str, str]):
        """添加翻译"""
        for lang, text in translations.items():
            if lang in self.supported_languages:
                if lang not in self.translations:
                    self.translations[lang] = {}
                self.translations[lang][key] = text
                
                # 保存到文件
                self._save_translation(lang, self.translations[lang])
    
    def update_translation(self, language: str, updates: Dict[str, str]):
        """更新翻译"""
        if language not in self.supported_languages:
            logger.warning(f"不支持的语言: {language}")
            return False
        
        if language not in self.translations:
            self.translations[language] = {}
        
        self.translations[language].update(updates)
        self._save_translation(language, self.translations[language])
        return True
    
    def get_supported_languages(self) -> list:
        """获取支持的语言列表"""
        return self.supported_languages.copy()
    
    def set_default_language(self, language: str):
        """设置默认语言"""
        if language in self.supported_languages:
            self.default_language = language
            logger.info(f"默认语言设置为: {language}")
        else:
            logger.warning(f"不支持的语言: {language}")
    
    def format_text(self, key: str, language: str = None, **kwargs) -> str:
        """格式化翻译文本"""
        text = self.get_text(key, language)
        try:
            return text.format(**kwargs)
        except (KeyError, ValueError) as e:
            logger.warning(f"文本格式化失败 {key}: {str(e)}")
            return text
