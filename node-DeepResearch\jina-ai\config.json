{"env": {"https_proxy": "", "OPENAI_BASE_URL": "", "GEMINI_API_KEY": "", "OPENAI_API_KEY": "", "JINA_API_KEY": "", "BRAVE_API_KEY": "", "SERPER_API_KEY": "", "DEFAULT_MODEL_NAME": ""}, "defaults": {"search_provider": "jina", "llm_provider": "vertex", "step_sleep": 500}, "providers": {"vertex": {"createClient": "createGoogleVertex", "clientConfig": {"location": "us-central1"}}, "gemini": {"createClient": "createGoogleGenerativeAI"}, "openai": {"createClient": "createOpenAI", "clientConfig": {"compatibility": "strict"}}}, "models": {"gemini": {"default": {"model": "gemini-2.0-flash", "temperature": 0.6, "maxTokens": 8000}, "tools": {"coder": {"maxTokens": 2000, "model": "gemini-2.0-flash-lite"}, "searchGrounding": {}, "dedup": {}, "evaluator": {"maxTokens": 2000}, "errorAnalyzer": {"maxTokens": 1000}, "queryRewriter": {"maxTokens": 2000}, "agent": {}, "agentBeastMode": {}, "fallback": {"maxTokens": 8000, "model": "gemini-2.0-flash-lite"}}}, "openai": {"default": {"model": "gpt-4o-mini", "temperature": 0, "maxTokens": 8000}, "tools": {"coder": {"temperature": 0.7}, "searchGrounding": {"temperature": 0}, "dedup": {"temperature": 0.1}, "evaluator": {}, "errorAnalyzer": {}, "queryRewriter": {"temperature": 0.1}, "agent": {"temperature": 0.7}, "agentBeastMode": {"temperature": 0.7}, "fallback": {"temperature": 0}}}}}