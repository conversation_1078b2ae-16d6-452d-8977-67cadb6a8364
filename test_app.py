#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for InsightPulse application
"""

import os
import sys
import asyncio
import logging

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported successfully"""
    print("Testing imports...")
    
    try:
        # Test core modules
        from core.llm_interface import LLMInterface
        from core.demand_analyzer import <PERSON><PERSON><PERSON><PERSON>y<PERSON>
        from core.ecosystem_scanner import EcosystemScanner
        from core.innovation_radar import InnovationRadar
        from core.decision_engine import DecisionEngine
        print("✅ Core modules imported successfully")
        
        # Test utility modules
        from utils.config import Config
        from utils.i18n import I18nManager
        from utils.logger import setup_logger
        print("✅ Utility modules imported successfully")
        
        # Test main app
        from app import InsightPulseApp, UserMode, AnalysisResult
        print("✅ Main app modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from utils.config import Config
        config = Config()
        
        # Check if config has required attributes
        assert hasattr(config, 'llm'), "Missing LLM config"
        assert hasattr(config, 'search'), "Missing search config"
        assert hasattr(config, 'app'), "Missing app config"
        
        print("✅ Configuration loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {str(e)}")
        return False

def test_i18n():
    """Test internationalization"""
    print("\nTesting internationalization...")
    
    try:
        from utils.i18n import I18nManager
        i18n = I18nManager()
        
        # Test getting text in different languages
        zh_text = i18n.get_text("app_title", "zh")
        en_text = i18n.get_text("app_title", "en")
        
        assert zh_text != en_text, "Chinese and English texts should be different"
        assert "逛逛" in zh_text or "InsightPulse" in zh_text, "Chinese title should contain expected text"
        assert "InsightPulse" in en_text, "English title should contain InsightPulse"
        
        print("✅ Internationalization working correctly")
        return True
        
    except Exception as e:
        print(f"❌ I18n error: {str(e)}")
        return False

def test_logger():
    """Test logging setup"""
    print("\nTesting logger...")
    
    try:
        from utils.logger import setup_logger
        logger = setup_logger("test_logger")
        
        # Test logging at different levels
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.error("Test error message")
        
        print("✅ Logger working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Logger error: {str(e)}")
        return False

async def test_app_initialization():
    """Test app initialization without API keys"""
    print("\nTesting app initialization...")
    
    try:
        # Set dummy environment variables to avoid API key errors
        os.environ['MODEL_API_KEY'] = 'dummy_key'
        os.environ['TAVILY_API_KEY'] = 'dummy_key'
        
        from app import InsightPulseApp
        app = InsightPulseApp()
        
        # Check if app has required components
        assert hasattr(app, 'config'), "Missing config"
        assert hasattr(app, 'i18n'), "Missing i18n"
        assert hasattr(app, 'llm_interface'), "Missing LLM interface"
        assert hasattr(app, 'demand_analyzer'), "Missing demand analyzer"
        assert hasattr(app, 'ecosystem_scanner'), "Missing ecosystem scanner"
        assert hasattr(app, 'innovation_radar'), "Missing innovation radar"
        assert hasattr(app, 'decision_engine'), "Missing decision engine"
        
        print("✅ App initialization successful")
        return True
        
    except Exception as e:
        print(f"❌ App initialization error: {str(e)}")
        return False

def test_gradio_interface():
    """Test Gradio interface creation"""
    print("\nTesting Gradio interface...")
    
    try:
        # Set dummy environment variables
        os.environ['MODEL_API_KEY'] = 'dummy_key'
        os.environ['TAVILY_API_KEY'] = 'dummy_key'
        
        from app import create_gradio_interface
        demo = create_gradio_interface()
        
        assert demo is not None, "Gradio interface should not be None"
        print("✅ Gradio interface created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Gradio interface error: {str(e)}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\nChecking dependencies...")
    
    required_packages = [
        'gradio',
        'openai', 
        'tavily',
        'pandas',
        'numpy',
        'requests',
        'asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tavily':
                import tavily
            elif package == 'asyncio':
                import asyncio
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - NOT INSTALLED")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    else:
        print("✅ All required dependencies are installed")
        return True

async def run_all_tests():
    """Run all tests"""
    print("🧪 Running InsightPulse Tests\n" + "="*50)
    
    tests = [
        ("Dependencies", check_dependencies),
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Internationalization", test_i18n),
        ("Logger", test_logger),
        ("App Initialization", test_app_initialization),
        ("Gradio Interface", test_gradio_interface)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
    
    print(f"\n" + "="*50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to run.")
        print("\nTo start the application:")
        print("1. Set your API keys in .env file")
        print("2. Run: python app.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    # Suppress some logging during tests
    logging.getLogger().setLevel(logging.WARNING)
    
    # Run tests
    asyncio.run(run_all_tests())
