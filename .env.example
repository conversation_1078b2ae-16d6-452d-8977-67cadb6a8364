# LLM Configuration
MODEL_API_KEY=your_openai_compatible_api_key_here
MODEL_BASE_URL=https://your-model-provider.com/v1

# Search Configuration  
TAVILY_API_KEY=your_tavily_api_key_here

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO

# Optional: Custom model endpoints
REASONING_MODEL=deepseek-ai/DeepSeek-R1
ANALYSIS_MODEL=Qwen/Qwen2.5-72B-Instruct
SUMMARY_MODEL=Qwen/Qwen2.5-72B-Instruct

# Optional: Performance tuning
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=60
CACHE_TTL=3600
