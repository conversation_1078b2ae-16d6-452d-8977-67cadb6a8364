# LLM API配置 - 支持多个提供商，不强制填满
# DeepSeek API
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# Claude API
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_BASE_URL=https://api.anthropic.com

# OpenAI API (官方)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# OpenAI兼容API (第三方服务商1)
OPENAI_COMPATIBLE_API_KEY=your_compatible_api_key_here
OPENAI_COMPATIBLE_BASE_URL=https://your-provider.com/v1

# OpenAI兼容API (第三方服务商2)
OPENAI_COMPATIBLE_API_KEY_2=your_compatible_api_key_2_here
OPENAI_COMPATIBLE_BASE_URL_2=https://your-provider-2.com/v1

# 搜索配置
TAVILY_API_KEY=your_tavily_api_key_here

# 应用配置
DEBUG=false
LOG_LEVEL=INFO

# API密钥检查开关 (默认关闭，允许无密钥启动)
REQUIRE_API_KEYS=false

# 可选：性能调优
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=60
CACHE_TTL=3600
