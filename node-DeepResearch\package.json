{"name": "node-deepresearch", "version": "1.0.0", "main": "dist/app.js", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "tsc", "dev": "npx ts-node src/agent.ts", "search": "npx ts-node src/test-duck.ts", "rewrite": "npx ts-node src/tools/query-rewriter.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "serve": "ts-node src/server.ts", "start": "ts-node src/server.ts", "eval": "ts-node src/evals/batch-evals.ts", "test": "jest --testTimeout=30000", "test:watch": "jest --watch", "test:docker": "jest src/__tests__/docker.test.ts --testTimeout=300000"}, "keywords": [], "author": "Jina AI", "license": "Apache-2.0", "description": "", "dependencies": {"@ai-sdk/google": "^1.0.0", "@ai-sdk/openai": "^1.1.9", "@types/jsdom": "^21.1.7", "ai": "^4.1.26", "axios": "^1.7.9", "commander": "^13.1.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "duck-duck-scrape": "^2.2.7", "express": "^4.21.2", "hjson": "^3.2.2", "jsdom": "^26.0.0", "node-fetch": "^3.3.2", "undici": "^7.3.0", "zod": "^3.22.4", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@types/commander": "^2.12.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/hjson": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^22.10.10", "@types/node-fetch": "^2.6.12", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "optionalDependencies": {"@ai-sdk/google-vertex": "^2.1.12"}}