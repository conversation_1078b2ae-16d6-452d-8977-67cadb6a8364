# 逛逛 (InsightPulse) 安装配置指南

## 快速开始

### 1. 环境准备

**系统要求:**
- Python 3.8 或更高版本
- 8GB+ 内存
- 稳定的网络连接

**检查Python版本:**
```bash
python --version
# 或
python3 --version
```

### 2. 获取API密钥

#### 2.1 LLM API密钥 (必需)

**选项1: 使用OpenAI兼容的API服务**
- OpenAI API: https://platform.openai.com/api-keys
- DeepSeek API: https://platform.deepseek.com/
- 其他兼容OpenAI格式的API服务

**选项2: 本地部署的模型**
- Ollama: https://ollama.ai/
- LocalAI: https://localai.io/
- vLLM: https://github.com/vllm-project/vllm

#### 2.2 Tavily搜索API密钥 (必需)

1. 访问 https://tavily.com/
2. 注册账户
3. 获取API密钥

### 3. 安装应用

#### 方法1: 直接运行 (推荐)

```bash
# 1. 下载项目文件
# 2. 进入项目目录
cd insightpulse

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量 (见下方)

# 5. 启动应用
python start.py
```

#### 方法2: 使用虚拟环境

```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量

# 5. 启动应用
python start.py
```

#### 方法3: 使用Docker

```bash
# 1. 构建镜像
docker build -t insightpulse .

# 2. 运行容器
docker run -p 7860:7860 \
  -e MODEL_API_KEY=your_llm_api_key \
  -e TAVILY_API_KEY=your_tavily_api_key \
  insightpulse
```

### 4. 环境变量配置

#### 方法1: 创建 .env 文件 (推荐)

在项目根目录创建 `.env` 文件:

```bash
# LLM配置
MODEL_API_KEY=your_llm_api_key_here
MODEL_BASE_URL=https://api.openai.com/v1

# 搜索配置
TAVILY_API_KEY=your_tavily_api_key_here

# 可选配置
DEBUG=false
LOG_LEVEL=INFO
```

#### 方法2: 系统环境变量

**Windows:**
```cmd
set MODEL_API_KEY=your_llm_api_key_here
set TAVILY_API_KEY=your_tavily_api_key_here
```

**macOS/Linux:**
```bash
export MODEL_API_KEY=your_llm_api_key_here
export TAVILY_API_KEY=your_tavily_api_key_here
```

### 5. 验证安装

运行测试脚本:
```bash
python test_app.py
```

如果所有测试通过，说明安装成功！

### 6. 启动应用

```bash
# 使用启动脚本 (推荐)
python start.py

# 或直接运行主程序
python app.py
```

应用启动后，在浏览器中访问: http://localhost:7860

## 详细配置

### 配置文件 (config.json)

应用会自动创建 `config.json` 配置文件，你可以根据需要修改:

```json
{
  "llm": {
    "api_key": "",
    "base_url": "",
    "models": {
      "reasoning": "deepseek-ai/DeepSeek-R1",
      "analysis": "Qwen/Qwen2.5-72B-Instruct",
      "summary": "Qwen/Qwen2.5-72B-Instruct"
    },
    "temperature": 0.6,
    "max_tokens": 4000
  },
  "search": {
    "tavily_api_key": "",
    "max_results": 10,
    "search_depth": "advanced"
  },
  "app": {
    "debug": false,
    "log_level": "INFO",
    "max_history": 100,
    "cache_enabled": true,
    "cache_ttl": 3600
  }
}
```

### 模型配置

#### 使用不同的LLM提供商

**DeepSeek API:**
```bash
MODEL_API_KEY=your_deepseek_api_key
MODEL_BASE_URL=https://api.deepseek.com/v1
```

**本地Ollama:**
```bash
MODEL_API_KEY=ollama
MODEL_BASE_URL=http://localhost:11434/v1
```

**其他OpenAI兼容API:**
```bash
MODEL_API_KEY=your_api_key
MODEL_BASE_URL=https://your-provider.com/v1
```

#### 自定义模型

在 `config.json` 中修改模型配置:

```json
{
  "llm": {
    "models": {
      "reasoning": "your-reasoning-model",
      "analysis": "your-analysis-model", 
      "summary": "your-summary-model"
    }
  }
}
```

### 国际化配置

应用支持中英文双语，语言文件位于 `locales/` 目录:

- `locales/zh.json` - 中文
- `locales/en.json` - 英文

你可以修改这些文件来自定义界面文本。

### 日志配置

日志文件保存在 `logs/` 目录，可以通过以下方式配置:

```bash
# 设置日志级别
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR

# 启用调试模式
DEBUG=true
```

## 常见问题

### Q1: 依赖安装失败

**解决方案:**
```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或使用conda
conda install --file requirements.txt
```

### Q2: API密钥错误

**检查项目:**
1. 确认API密钥正确
2. 检查API服务是否可用
3. 验证网络连接
4. 查看日志文件获取详细错误信息

### Q3: 端口被占用

**解决方案:**
```bash
# 查看端口占用
netstat -ano | findstr :7860  # Windows
lsof -i :7860                 # macOS/Linux

# 修改端口 (在app.py中)
demo.launch(server_port=8080)
```

### Q4: 内存不足

**解决方案:**
1. 关闭其他应用程序
2. 减少并发请求数量
3. 使用更轻量的模型
4. 增加系统内存

### Q5: 搜索功能不工作

**检查项目:**
1. 确认Tavily API密钥正确
2. 检查网络连接
3. 验证API配额是否充足

## 性能优化

### 1. 缓存配置

```json
{
  "app": {
    "cache_enabled": true,
    "cache_ttl": 3600
  }
}
```

### 2. 并发控制

```bash
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=60
```

### 3. 模型选择

- 使用更快的模型进行初步分析
- 为重要分析使用更强大的模型
- 考虑本地部署减少网络延迟

## 部署到生产环境

### 使用Gunicorn (推荐)

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:7860 app:app
```

### 使用Nginx反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:7860;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 环境变量安全

生产环境中，建议使用密钥管理服务:
- AWS Secrets Manager
- Azure Key Vault  
- HashiCorp Vault
- Kubernetes Secrets

## 获取帮助

如果遇到问题:

1. 查看 `logs/` 目录中的日志文件
2. 运行 `python test_app.py` 诊断问题
3. 查看 [README.md](README.md) 获取更多信息
4. 在GitHub Issues中报告问题

---

**祝您使用愉快！** 🚀
