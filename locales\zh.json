{"app_title": "逛逛 (InsightPulse)", "app_subtitle": "需求探索与可行性分析平台", "app_description": "连接潜在用户需求与可行的解决方案，赋能创新者、开发者及普通用户", "user_mode": "用户模式", "regular_mode": "🔍 普通用户模式 - 寻找解决方案", "developer_mode": "🚀 开发者模式 - 探索创新机会", "regular_mode_desc": "为您推荐现有的解决方案和产品", "developer_mode_desc": "分析市场机会、竞品情况和技术可行性", "language": "语言", "analyze_button": "🔍 分析", "clear_history": "🗑️ 清除历史", "input_placeholder": "请描述您的需求，例如：有什么好用的笔记软件？", "result_placeholder": "在这里查看分析结果...", "example_note_software": "📝 有什么好用的笔记软件？", "example_fitness_app": "🏃‍♂️ 推荐一些健身APP", "example_ai_writing": "💡 AI写作助手的市场机会", "example_task_manager": "🔧 开发一个任务管理工具", "core_features": "🌟 核心功能", "feature_demand_analysis": "🔍 智能需求解析", "feature_ecosystem_scan": "🌐 生态扫描分析", "feature_competitor_analysis": "📊 竞品对比评估", "feature_innovation_radar": "💡 创新机会识别", "feature_feasibility_score": "⚡ 可行性评分", "feature_decision_support": "📋 决策建议生成", "analyzing": "正在分析", "analysis_complete": "分析完成", "analysis_error": "分析出错", "solution_recommendations": "解决方案推荐", "innovation_analysis": "创新机会分析", "demand_understanding": "需求理解", "feasibility_assessment": "📊 可行性评估", "confidence_level": "置信度", "market_opportunity": "💡 市场机会", "technical_feasibility": "⚙️ 技术可行性", "main_competitors": "🏢 主要竞品", "reference_materials": "📚 参考资料", "high": "高", "medium": "中", "low": "低", "unknown": "未知", "tip_select_mode": "💡 提示：选择用户模式后，输入您的需求开始分析", "history_cleared": "✅ 历史记录已清除", "export_coming_soon": "分析报告导出功能即将上线", "input_required": "请输入您的需求描述", "analysis_failed": "分析失败，请重试", "network_error": "网络连接错误", "api_error": "API调用失败", "learn_more": "🔗 了解更多", "score": "评分", "category": "分类", "users": "用户", "uncategorized": "未分类"}