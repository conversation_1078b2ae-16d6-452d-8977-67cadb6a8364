{"app_title": "InsightPulse", "app_subtitle": "Demand Exploration and Feasibility Analysis Platform", "app_description": "Connecting potential user needs with viable solutions, empowering innovators, developers and regular users", "user_mode": "User Mode", "regular_mode": "🔍 Regular User Mode - Find Solutions", "developer_mode": "🚀 Developer Mode - Explore Innovation Opportunities", "regular_mode_desc": "Recommend existing solutions and products for you", "developer_mode_desc": "Analyze market opportunities, competitors and technical feasibility", "language": "Language", "analyze_button": "🔍 Analyze", "clear_history": "🗑️ Clear History", "input_placeholder": "Please describe your needs, e.g.: What are some good note-taking software?", "result_placeholder": "View analysis results here...", "example_note_software": "📝 What are some good note-taking software?", "example_fitness_app": "🏃‍♂️ Recommend some fitness apps", "example_ai_writing": "💡 Market opportunities for AI writing assistants", "example_task_manager": "🔧 Develop a task management tool", "core_features": "🌟 Core Features", "feature_demand_analysis": "🔍 Intelligent Demand Analysis", "feature_ecosystem_scan": "🌐 Ecosystem Scanning", "feature_competitor_analysis": "📊 Competitive Analysis", "feature_innovation_radar": "💡 Innovation Opportunity Identification", "feature_feasibility_score": "⚡ Feasibility Scoring", "feature_decision_support": "📋 Decision Support Generation", "analyzing": "Analyzing", "analysis_complete": "Analysis Complete", "analysis_error": "Analysis Error", "solution_recommendations": "Solution Recommendations", "innovation_analysis": "Innovation Opportunity Analysis", "demand_understanding": "Demand Understanding", "feasibility_assessment": "📊 Feasibility Assessment", "confidence_level": "Confidence Level", "market_opportunity": "💡 Market Opportunity", "technical_feasibility": "⚙️ Technical Feasibility", "main_competitors": "🏢 Main Competitors", "reference_materials": "📚 Reference Materials", "high": "High", "medium": "Medium", "low": "Low", "unknown": "Unknown", "tip_select_mode": "💡 Tip: Select user mode and input your requirements to start analysis", "history_cleared": "✅ History cleared", "export_coming_soon": "Analysis report export feature coming soon", "input_required": "Please enter your requirement description", "analysis_failed": "Analysis failed, please try again", "network_error": "Network connection error", "api_error": "API call failed", "learn_more": "🔗 Learn More", "score": "Score", "category": "Category", "users": "Users", "uncategorized": "Uncategorized"}