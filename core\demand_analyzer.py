#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demand Analyzer Module
需求解析引擎 - 负责理解和解构用户需求
"""

import json
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class DemandAnalysisResult:
    """需求分析结果"""
    intent: str  # 用户意图
    category: str  # 需求领域
    keywords: List[str]  # 关键词
    constraints: List[str]  # 约束条件
    priority_features: List[str]  # 优先功能特性
    user_type: str  # 用户类型
    clarity_score: float  # 需求清晰度评分
    summary: str  # 需求摘要
    search_queries: List[str]  # 生成的搜索查询

class DemandAnalyzer:
    """需求解析引擎"""
    
    def __init__(self, llm_interface):
        """初始化需求分析器"""
        self.llm = llm_interface
        
        # 需求分类映射
        self.categories = {
            "productivity": ["效率工具", "办公软件", "任务管理", "笔记软件", "时间管理"],
            "entertainment": ["娱乐", "游戏", "音乐", "视频", "社交"],
            "education": ["教育", "学习", "培训", "课程", "技能"],
            "health": ["健康", "医疗", "健身", "运动", "心理"],
            "finance": ["金融", "理财", "投资", "支付", "记账"],
            "shopping": ["购物", "电商", "比价", "优惠", "团购"],
            "travel": ["旅行", "出行", "导航", "酒店", "机票"],
            "communication": ["通讯", "聊天", "邮件", "视频通话", "协作"],
            "development": ["开发", "编程", "技术", "工具", "框架"],
            "design": ["设计", "创意", "图像", "视频制作", "UI/UX"]
        }
        
        logger.info("需求分析器初始化完成")
    
    async def analyze(
        self,
        user_input: str,
        user_mode: str,
        language: str = "zh"
    ) -> Dict[str, Any]:
        """
        分析用户需求
        
        Args:
            user_input: 用户输入的需求描述
            user_mode: 用户模式 (regular/developer)
            language: 语言
            
        Returns:
            需求分析结果字典
        """
        try:
            logger.info(f"开始分析需求: {user_input[:50]}... (模式: {user_mode})")

            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(user_input, user_mode, language)
            
            # 调用LLM进行分析
            response = await self.llm.generate_analysis(analysis_prompt)
            
            # 解析响应
            analysis_result = await self.llm.parse_json_response(response.content)
            
            # 验证和补充分析结果
            validated_result = self._validate_and_enhance_result(
                analysis_result, user_input, user_mode
            )
            
            logger.info("需求分析完成")
            return validated_result
            
        except Exception as e:
            logger.error(f"需求分析失败: {str(e)}")
            # 返回基础分析结果
            return self._create_fallback_result(user_input, user_mode)
    
    def _build_analysis_prompt(self, user_input: str, user_mode: str, language: str) -> str:
        """构建需求分析提示词"""
        
        if language == "zh":
            mode_description = "寻求解决方案" if user_mode == "regular" else "探索创新机会"
            
            prompt = f"""作为需求分析专家，请深度分析以下用户需求。用户模式：{mode_description}

用户输入："{user_input}"

请按照以下JSON格式返回分析结果：

{{
    "intent": "用户的核心意图（如：寻求推荐、了解市场、技术咨询等）",
    "category": "需求所属领域（如：效率工具、娱乐、教育、健康等）",
    "keywords": ["提取的关键词列表"],
    "constraints": ["识别的约束条件（如：预算、平台、功能要求等）"],
    "priority_features": ["用户最关心的功能特性"],
    "user_type": "推断的用户类型（如：个人用户、企业用户、开发者等）",
    "clarity_score": 0.8,
    "summary": "需求的简洁摘要",
    "search_queries": ["为后续搜索生成的查询词"]
}}

分析要求：
1. 深度理解用户真实需求和潜在动机
2. 识别明确和隐含的约束条件
3. 根据用户模式调整分析重点
4. 生成有效的搜索查询词
5. 评估需求描述的清晰度（0-1分）

请确保返回有效的JSON格式。"""

        else:  # English
            mode_description = "seeking solutions" if user_mode == "regular" else "exploring innovation opportunities"
            
            prompt = f"""As a demand analysis expert, please deeply analyze the following user requirement. User mode: {mode_description}

User input: "{user_input}"

Please return the analysis result in the following JSON format:

{{
    "intent": "User's core intent (e.g., seeking recommendations, market research, technical consultation, etc.)",
    "category": "Requirement domain (e.g., productivity tools, entertainment, education, health, etc.)",
    "keywords": ["List of extracted keywords"],
    "constraints": ["Identified constraints (e.g., budget, platform, feature requirements, etc.)"],
    "priority_features": ["Features that users care about most"],
    "user_type": "Inferred user type (e.g., individual user, enterprise user, developer, etc.)",
    "clarity_score": 0.8,
    "summary": "Concise summary of the requirement",
    "search_queries": ["Query terms generated for subsequent searches"]
}}

Analysis requirements:
1. Deeply understand user's real needs and potential motivations
2. Identify explicit and implicit constraints
3. Adjust analysis focus based on user mode
4. Generate effective search query terms
5. Evaluate clarity of requirement description (0-1 score)

Please ensure valid JSON format is returned."""
        
        return prompt
    
    def _validate_and_enhance_result(
        self, 
        analysis_result: Dict[str, Any], 
        user_input: str, 
        user_mode
    ) -> Dict[str, Any]:
        """验证和增强分析结果"""
        
        # 设置默认值
        result = {
            "intent": analysis_result.get("intent", "需求咨询"),
            "category": analysis_result.get("category", "未分类"),
            "keywords": analysis_result.get("keywords", []),
            "constraints": analysis_result.get("constraints", []),
            "priority_features": analysis_result.get("priority_features", []),
            "user_type": analysis_result.get("user_type", "个人用户"),
            "clarity_score": float(analysis_result.get("clarity_score", 0.5)),
            "summary": analysis_result.get("summary", user_input[:100]),
            "search_queries": analysis_result.get("search_queries", []),
            "original_input": user_input,
            "user_mode": user_mode.value,
            "analysis_timestamp": self._get_timestamp()
        }
        
        # 如果没有关键词，从输入中提取
        if not result["keywords"]:
            result["keywords"] = self._extract_basic_keywords(user_input)
        
        # 如果没有搜索查询，生成基础查询
        if not result["search_queries"]:
            result["search_queries"] = self._generate_basic_queries(user_input, result["keywords"])
        
        # 限制列表长度
        result["keywords"] = result["keywords"][:10]
        result["search_queries"] = result["search_queries"][:5]
        result["constraints"] = result["constraints"][:8]
        result["priority_features"] = result["priority_features"][:8]
        
        return result
    
    def _create_fallback_result(self, user_input: str, user_mode: str) -> Dict[str, Any]:
        """创建备用分析结果"""
        keywords = self._extract_basic_keywords(user_input)
        
        return {
            "intent": "需求咨询",
            "category": "未分类",
            "keywords": keywords,
            "constraints": [],
            "priority_features": [],
            "user_type": "个人用户",
            "clarity_score": 0.3,
            "summary": user_input[:100],
            "search_queries": self._generate_basic_queries(user_input, keywords),
            "original_input": user_input,
            "user_mode": user_mode,
            "analysis_timestamp": self._get_timestamp(),
            "fallback": True
        }
    
    def _extract_basic_keywords(self, text: str) -> List[str]:
        """提取基础关键词"""
        import re
        
        # 简单的关键词提取
        # 移除标点符号，分割单词
        words = re.findall(r'\b\w+\b', text.lower())
        
        # 过滤停用词
        stop_words = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这", "那", "什么", "可以", "怎么", "为什么", "哪里", "怎样",
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "what", "where", "when", "why", "how"
        }
        
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        # 去重并限制数量
        return list(dict.fromkeys(keywords))[:10]
    
    def _generate_basic_queries(self, user_input: str, keywords: List[str]) -> List[str]:
        """生成基础搜索查询"""
        queries = []
        
        # 使用原始输入
        queries.append(user_input)
        
        # 使用关键词组合
        if len(keywords) >= 2:
            queries.append(" ".join(keywords[:3]))
        
        # 添加常见搜索模式
        if "软件" in user_input or "app" in user_input.lower():
            queries.append(f"{' '.join(keywords[:2])} 软件推荐")
            queries.append(f"{' '.join(keywords[:2])} app")
        
        if "工具" in user_input or "tool" in user_input.lower():
            queries.append(f"{' '.join(keywords[:2])} 工具")
        
        return queries[:5]
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
