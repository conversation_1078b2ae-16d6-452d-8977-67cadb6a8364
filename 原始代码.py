import gradio as gr

from openai import OpenAI

from typing import Dict, Any, List

import html

import os

from tavily import TavilyClient



# 初始化OpenAI客户端

client = OpenAI(

api_key = os.getenv('MODEL_API_KEY'),

base_url = "",

)



# 初始化Tavily客户端

tavily_client = TavilyClient(api_key=os.getenv('TAVILY_API_KEY'))



# 存储聊天历史

chat_messages = []



def get_chat_messages() -> List[Dict[str, str]]:

"""获取聊天历史消息"""

global chat_messages

return chat_messages



def predict(message, chatbot):

"""处理用户输入并生成回答"""

global chat_messages

messages = get_chat_messages()

# 添加用户消息

messages.append({"role": "user", "content": message + "，你不能修改用户问题，你只需要深度思考即可。"})


# 更新聊天界面显示用户消息

chatbot.append({"role": "user", "content": message})

yield chatbot


try:

# 调用DeepSeek R1进行思考

reasoning_content = ""

thinking_html = "<small>🤔 思考中...</small>"

yield chatbot[:-1] + [{"role": "user", "content": message}, {"role": "assistant", "content": thinking_html}]


r1_stream = client.chat.completions.create(

model="deepseek-ai/DeepSeek-R1",

messages=messages,

temperature=0.6,

stream=True

)


# 实时显示思考过程

reasoning_content = ""

for chunk in r1_stream:

if chunk.choices and chunk.choices[0].delta:

delta = chunk.choices[0].delta

if hasattr(delta, 'reasoning_content') and delta.reasoning_content:

reasoning_content += delta.reasoning_content

thinking_html = f"<div style='border: 1px solid var(--border-color-primary); padding: 16px; border-radius: 8px; background-color: var(--background-fill-secondary);'><div style='font-weight: bold; color: var(--color-accent); margin-bottom: 12px;'>🔍 DeepSeek R1 思考过程</div><div style='color: var(--body-text-color); font-size: 0.9em; margin-bottom: 8px;'>{html.escape(reasoning_content)}</div></div>"

yield chatbot[:-1] + [{"role": "user", "content": message}, {"role": "assistant", "content": thinking_html}]

elif delta.content:

break



# 使用Tavily进行搜索

search_info = "\n搜索结果：\n"

search_result = []

try:

search_results = tavily_client.search(message, search_depth="advanced")

search_result = [{

"index": idx + 1,

"url": result.get("url", ""),

"title": result.get("title", "")

} for idx, result in enumerate(search_results.get("results", []))]


for idx, result in enumerate(search_result):

search_info += f"{idx + 1}. 标题：{result.get('title', '')}\n 链接：{result.get('url', '')}\n"

except Exception as e:

print(f"Tavily搜索出错: {str(e)}")

search_info = "\n搜索过程中出现错误，将继续使用已有信息进行回答。\n"



# 将搜索结果添加到用户消息中

messages[-1]["content"] = message + search_info



qwen_messages = messages + [

{"role": "assistant", "content": reasoning_content},

{"role": "user", "content": "你必须根据以上思考过程和搜索结果，给出详细且准确的回答。在回答时，请明确引用相关的搜索结果来支持你的观点。"}

]


# 调用Qwen生成回答（启用搜索增强）

qwen_stream = client.chat.completions.create(

model="Qwen/Qwen2.5-72B-Instruct",

messages=qwen_messages,

stream=True,

)


# 流式显示Qwen的回答

full_response = ""

qwen_html = f"<div style='border: 2px solid var(--border-color-primary); padding: 16px; border-radius: 8px; background-color: var(--background-fill-secondary); margin-top: 24px;'><div style='font-weight: bold; color: var(--color-accent); font-size: 1.2em; margin-bottom: 12px;'>🤖 Qwen 2.5 正在生成回答...</div></div>"

yield chatbot[:-1] + [{"role": "user", "content": message}, {"role": "assistant", "content": thinking_html + qwen_html}]


for chunk in qwen_stream:

if chunk.choices and chunk.choices[0].delta:

delta = chunk.choices[0].delta

if hasattr(delta, 'content') and delta.content:

full_response += delta.content

qwen_html = f"<div style='border: 2px solid var(--border-color-primary); padding: 16px; border-radius: 8px; background-color: var(--background-fill-secondary); margin-top: 24px;'><div style='font-weight: bold; color: var(--color-accent); font-size: 1.2em; margin-bottom: 12px;'>🤖 Qwen 2.5 最终回答</div><div style='color: var(--body-text-color); font-size: 1.1em; line-height: 1.6;'>{html.escape(full_response)}▌</div></div>"

yield chatbot[:-1] + [{"role": "user", "content": message}, {"role": "assistant", "content": thinking_html + qwen_html}]





# 显示最终回答和搜索结果

final_content = f"<div style='border: 2px solid var(--border-color-primary); padding: 16px; border-radius: 8px; background-color: var(--background-fill-secondary); margin-top: 24px;'>"

final_content += f"<div style='font-weight: bold; color: var(--color-accent); font-size: 1.2em; margin-bottom: 12px;'>🤖 Qwen 2.5 的总结</div>"

final_content += f"<div style='color: var(--body-text-color); font-size: 1.1em; line-height: 1.6;'>{html.escape(full_response)}</div>"


if search_result:

final_content += f"<div style='margin-top: 16px; padding-top: 16px; border-top: 1px solid var(--border-color-primary);'>"

final_content += f"<div style='font-weight: bold; color: var(--color-accent); margin-bottom: 12px;'>📚 参考资料</div>"

# 使用字典去重搜索结果

unique_dict = {}

for item in search_result:

unique_dict[item["index"]] = item


# 显示去重后的搜索结果

for result in list(unique_dict.values()):

final_content += f"<div style='margin-bottom: 12px; padding: 8px; background-color: var(--background-fill-primary); border-radius: 4px;'>"

final_content += f"<div style='font-size: 1em; margin-bottom: 4px;'>{str(result['index'])}. <a href='{result['url']}' target='_blank' style='color: var(--color-accent); text-decoration: none; font-weight: 500;'>{result['title']}</a></div>"

final_content += f"</div>"

final_content += "</div>"


final_content += "</div>"


# 更新聊天界面显示最终回答

yield chatbot[:-1] + [{"role": "user", "content": message}, {"role": "assistant", "content": thinking_html + final_content}]


# 保存助手回答到会话历史

messages.append({"role": "assistant", "content": full_response})

return chatbot


except Exception as e:

error_message = f"发生错误: {str(e)}"

yield chatbot[:-1] + [{"role": "user", "content": message}, {"role": "assistant", "content": f"<div style='color: red;'>{error_message}</div>"}]

return chatbot



def reset_chat():

"""重置聊天历史"""

global chat_messages

chat_messages = []

return [], []



# 创建Gradio界面

with gr.Blocks(title="DeepSeek-TavilySearch-Qwen深度搜索总结助手 🤖", theme=gr.themes.Soft()) as demo:

gr.HTML(

"""

<div style="text-align: center; margin-bottom: 1rem">

<h1>DeepSeek-TavilySearch-Qwen深度搜索总结助手 🤖</h1>

</div>

"""

)


# 添加应用介绍

gr.HTML(

"""

<div style='font-size: 1.1em; color: var(--body-text-color); line-height: 1.6; margin-bottom: 24px; padding: 16px; border-radius: 8px; background-color: var(--background-fill-secondary);'>

这是一款通过DeepSeek R1 模型的强大推理能力与Tavily搜索和Qwen 2.5模型强大总结能力相结合的应用，为用户提供精准和专业的问答体验。

</div>

"""

)


# 聊天界面

chatbot = gr.Chatbot(height=500, show_label=False, elem_id="chatbot", render=True, type="messages")

with gr.Row():

with gr.Column(scale=4):

msg = gr.Textbox(

show_label=False,

placeholder="请输入您的问题...",

container=False

)

# 添加示例问题按钮

with gr.Row():

example1 = gr.Button("英伟达发布GB300之后为什么股价反而跌了？")

example2 = gr.Button("请预测一下哪吒之魔童闹海的票房？")

with gr.Column(scale=1):

with gr.Row():

submit = gr.Button("发送")

clear = gr.Button("🗑️ 清除历史")


# 设置事件处理

msg.submit(predict, [msg, chatbot], [chatbot], queue=True).then(

lambda: "", None, [msg]

)

submit.click(predict, [msg, chatbot], [chatbot], queue=True).then(

lambda: "", None, [msg]

)

clear.click(reset_chat, None, [chatbot, msg], queue=False)


# 添加示例问题点击事件

example1.click(lambda: "英伟达发布GB300之后为什么股价反而跌了？", None, msg)

example2.click(lambda: "请预测一下哪吒之魔童闹海的票房？", None, msg)



# 启动应用

if __name__ == "__main__":

demo.queue()

demo.launch()