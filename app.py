#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逛逛 (InsightPulse) - 需求探索与可行性分析平台
Demand Exploration and Feasibility Analysis Platform

Version: 1.0.0
Author: DreamWhile
License: MIT
"""

import gradio as gr
import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

# Core engine imports
from core.demand_analyzer import DemandAnalyzer
from core.ecosystem_scanner import EcosystemScanner
from core.innovation_radar import InnovationRadar
from core.decision_engine import DecisionEngine
from core.llm_interface import LLMInterface

# Utility imports
from utils.i18n import I18nManager
from utils.config import Config
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

@dataclass
class AnalysisResult:
    """分析结果数据类"""
    demand_analysis: Dict[str, Any]
    ecosystem_scan: Dict[str, Any]
    innovation_assessment: Dict[str, Any]
    recommendations: List[Dict[str, Any]]
    feasibility_score: float
    confidence_level: float
    references: List[Dict[str, Any]]
    market_analysis: Optional[Dict[str, Any]] = None  # 市场分析（追问时生成）

class InsightPulseApp:
    """逛逛平台主应用类"""
    
    def __init__(self):
        """初始化应用"""
        self.config = Config()
        self.i18n = I18nManager()
        
        # 初始化核心引擎
        self.llm_interface = LLMInterface(self.config)
        self.demand_analyzer = DemandAnalyzer(self.llm_interface)
        self.ecosystem_scanner = EcosystemScanner(self.config)
        self.innovation_radar = InnovationRadar(self.llm_interface)
        self.decision_engine = DecisionEngine(self.llm_interface)
        
        # 应用状态
        self.current_language = "zh"
        self.analysis_history = []
        
        logger.info("InsightPulse应用初始化完成")
    
    async def analyze_demand(
        self,
        user_input: str,
        language: str = "zh"
    ) -> AnalysisResult:
        """
        核心需求分析流程

        Args:
            user_input: 用户输入的需求描述
            language: 界面语言

        Returns:
            AnalysisResult: 完整的分析结果
        """
        try:
            self.current_language = language

            logger.info(f"开始分析需求: {user_input[:50]}...")

            # 1. 需求解析 (默认为普通用户模式)
            demand_analysis = await self.demand_analyzer.analyze(
                user_input, "regular", language
            )

            # 2. 生态扫描
            ecosystem_scan = await self.ecosystem_scanner.scan(
                demand_analysis, language
            )

            # 3. 创新雷达分析 (基础分析)
            innovation_assessment = await self.innovation_radar.assess(
                demand_analysis, ecosystem_scan, "regular"
            )

            # 4. 决策建议生成 (普通用户推荐)
            recommendations = await self.decision_engine.generate_recommendations(
                demand_analysis, ecosystem_scan, innovation_assessment, "regular", language
            )

            # 5. 计算可行性评分
            feasibility_score = self._calculate_feasibility_score(
                ecosystem_scan, innovation_assessment
            )

            # 6. 计算置信度
            confidence_level = self._calculate_confidence_level(
                demand_analysis, ecosystem_scan
            )

            # 7. 整理参考资料
            references = self._collect_references(
                ecosystem_scan, innovation_assessment
            )

            result = AnalysisResult(
                demand_analysis=demand_analysis,
                ecosystem_scan=ecosystem_scan,
                innovation_assessment=innovation_assessment,
                recommendations=recommendations,
                feasibility_score=feasibility_score,
                confidence_level=confidence_level,
                references=references
            )

            # 保存到历史记录
            self.analysis_history.append({
                "input": user_input,
                "result": result,
                "timestamp": self._get_timestamp()
            })

            logger.info("需求分析完成")
            return result

        except Exception as e:
            logger.error(f"需求分析失败: {str(e)}")
            raise

    async def generate_market_analysis(
        self,
        original_result: AnalysisResult,
        language: str = "zh"
    ) -> Dict[str, Any]:
        """
        生成市场分析（追问功能）

        Args:
            original_result: 原始分析结果
            language: 界面语言

        Returns:
            市场分析结果
        """
        try:
            logger.info("开始生成市场分析...")

            # 使用开发者模式重新分析
            market_analysis = await self.innovation_radar.assess(
                original_result.demand_analysis,
                original_result.ecosystem_scan,
                "developer"
            )

            # 生成开发者建议
            developer_recommendations = await self.decision_engine.generate_recommendations(
                original_result.demand_analysis,
                original_result.ecosystem_scan,
                market_analysis,
                "developer",
                language
            )

            return {
                "market_opportunity": market_analysis.get("market_opportunity", {}),
                "technical_feasibility": market_analysis.get("technical_feasibility", {}),
                "competitive_analysis": market_analysis.get("competitive_analysis", {}),
                "resource_requirements": market_analysis.get("resource_requirements", {}),
                "risk_factors": market_analysis.get("risk_factors", {}),
                "developer_recommendations": developer_recommendations,
                "overall_score": market_analysis.get("overall_score", 0.5)
            }

        except Exception as e:
            logger.error(f"市场分析生成失败: {str(e)}")
            return {
                "error": "市场分析生成失败",
                "message": str(e)
            }
    
    def _calculate_feasibility_score(
        self, 
        ecosystem_scan: Dict[str, Any], 
        innovation_assessment: Dict[str, Any]
    ) -> float:
        """计算可行性评分"""
        # 基于竞品数量、市场成熟度、技术难度等因素计算
        competitors_count = len(ecosystem_scan.get("competitors", []))
        market_maturity = ecosystem_scan.get("market_maturity", 0.5)
        technical_difficulty = innovation_assessment.get("technical_difficulty", 0.5)
        
        # 简化的评分算法
        base_score = 0.5
        if competitors_count > 5:
            base_score -= 0.2  # 竞争激烈降分
        elif competitors_count < 2:
            base_score += 0.1  # 蓝海市场加分
            
        base_score += market_maturity * 0.3
        base_score -= technical_difficulty * 0.2
        
        return max(0.0, min(1.0, base_score))
    
    def _calculate_confidence_level(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> float:
        """计算置信度"""
        # 基于数据质量、搜索结果数量等计算置信度
        search_results_count = len(ecosystem_scan.get("search_results", []))
        demand_clarity = demand_analysis.get("clarity_score", 0.5)
        
        confidence = 0.3 + (search_results_count / 20) * 0.4 + demand_clarity * 0.3
        return max(0.0, min(1.0, confidence))
    
    def _collect_references(
        self, 
        ecosystem_scan: Dict[str, Any], 
        innovation_assessment: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """收集参考资料"""
        references = []
        
        # 从生态扫描结果中提取参考资料
        for result in ecosystem_scan.get("search_results", []):
            references.append({
                "title": result.get("title", ""),
                "url": result.get("url", ""),
                "source": "ecosystem_scan",
                "relevance": result.get("relevance", 0.5)
            })
        
        # 从创新评估中提取参考资料
        for case in innovation_assessment.get("historical_cases", []):
            references.append({
                "title": case.get("title", ""),
                "url": case.get("url", ""),
                "source": "historical_analysis",
                "relevance": case.get("relevance", 0.5)
            })
        
        # 按相关性排序并限制数量
        references.sort(key=lambda x: x["relevance"], reverse=True)
        return references[:10]
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

# 全局应用实例
app_instance = None

def get_app_instance():
    """获取应用实例（单例模式）"""
    global app_instance
    if app_instance is None:
        app_instance = InsightPulseApp()
    return app_instance

# 全局变量存储最后的分析结果
last_analysis_result = None

# Gradio界面处理函数
async def process_user_input(
    user_input: str,
    language: str,
    chatbot_history: List[Dict[str, str]]
) -> Tuple[List[Dict[str, str]], str]:
    """
    处理用户输入的主函数

    Args:
        user_input: 用户输入
        language: 界面语言
        chatbot_history: 聊天历史

    Returns:
        更新后的聊天历史和状态信息
    """
    global last_analysis_result

    if not user_input.strip():
        return chatbot_history, "请输入您的需求描述"

    app = get_app_instance()

    # 添加用户消息到聊天历史
    chatbot_history.append({
        "role": "user",
        "content": user_input
    })

    # 添加分析中的提示
    analyzing_msg = app.i18n.get_text("analyzing", language)
    chatbot_history.append({
        "role": "assistant",
        "content": f"🔍 {analyzing_msg}..."
    })

    try:
        # 执行需求分析
        result = await app.analyze_demand(user_input, language)
        last_analysis_result = result  # 保存结果供追问使用

        # 格式化分析结果
        formatted_result = format_analysis_result(result, language)

        # 更新聊天历史
        chatbot_history[-1] = {
            "role": "assistant",
            "content": formatted_result
        }

        status = app.i18n.get_text("analysis_complete", language)

    except Exception as e:
        error_msg = app.i18n.get_text("analysis_error", language)
        chatbot_history[-1] = {
            "role": "assistant",
            "content": f"❌ {error_msg}: {str(e)}"
        }
        status = f"错误: {str(e)}"

    return chatbot_history, status

async def process_market_analysis(
    language: str,
    chatbot_history: List[Dict[str, str]]
) -> Tuple[List[Dict[str, str]], str]:
    """
    处理市场分析追问

    Args:
        language: 界面语言
        chatbot_history: 聊天历史

    Returns:
        更新后的聊天历史和状态信息
    """
    global last_analysis_result

    if not last_analysis_result:
        return chatbot_history, "请先进行需求分析"

    app = get_app_instance()

    # 添加市场分析请求到聊天历史
    chatbot_history.append({
        "role": "user",
        "content": "📊 请提供市场分析和开发建议"
    })

    # 添加分析中的提示
    chatbot_history.append({
        "role": "assistant",
        "content": "🔍 正在生成市场分析..."
    })

    try:
        # 生成市场分析
        market_analysis = await app.generate_market_analysis(last_analysis_result, language)

        # 格式化市场分析结果
        formatted_result = format_market_analysis_result(market_analysis, language)

        # 更新聊天历史
        chatbot_history[-1] = {
            "role": "assistant",
            "content": formatted_result
        }

        status = "市场分析完成"

    except Exception as e:
        chatbot_history[-1] = {
            "role": "assistant",
            "content": f"❌ 市场分析生成失败: {str(e)}"
        }
        status = f"错误: {str(e)}"

    return chatbot_history, status

def format_analysis_result(result: AnalysisResult, language: str) -> str:
    """格式化分析结果为HTML显示"""
    app = get_app_instance()

    # 统一使用普通用户格式，并在末尾添加市场分析按钮
    formatted_result = format_regular_user_result(result, language, app.i18n)

    # 添加市场分析追问按钮
    market_analysis_button = f"""
    <div style='margin-top: 20px; padding: 16px; background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
                border-radius: 12px; text-align: center;'>
        <h3 style='color: white; margin-bottom: 12px;'>💼 为产品经理和开发者提供</h3>
        <p style='color: white; margin-bottom: 16px; opacity: 0.9;'>
            想了解市场机会、技术可行性和竞争分析？点击下方按钮获取专业的市场分析报告。
        </p>
        <div style='background: rgba(255,255,255,0.2); padding: 8px; border-radius: 8px;
                    border: 1px dashed rgba(255,255,255,0.5);'>
            <p style='color: white; margin: 0; font-size: 0.9em;'>
                💡 提示：点击下方"📊 市场分析"按钮获取详细的开发建议
            </p>
        </div>
    </div>
    """

    return formatted_result + market_analysis_button

def format_regular_user_result(result: AnalysisResult, language: str, i18n: I18nManager) -> str:
    """格式化普通用户模式的结果"""
    html_parts = []

    # 标题
    title = i18n.get_text("solution_recommendations", language)
    html_parts.append(f"""
    <div style='border: 2px solid #4CAF50; padding: 20px; border-radius: 12px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); margin-bottom: 20px;'>
        <h2 style='color: #2E7D32; margin-bottom: 16px; display: flex; align-items: center;'>
            🎯 {title}
        </h2>
    """)

    # 需求分析摘要
    demand_summary = result.demand_analysis.get("summary", "")
    if demand_summary:
        html_parts.append(f"""
        <div style='background: white; padding: 12px; border-radius: 8px; margin-bottom: 16px;
                    border-left: 4px solid #4CAF50;'>
            <strong>需求理解:</strong> {demand_summary}
        </div>
        """)

    # 推荐解决方案
    recommendations = result.recommendations[:3]  # 显示前3个推荐
    for i, rec in enumerate(recommendations, 1):
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 12px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
            <h3 style='color: #1976D2; margin-bottom: 8px;'>
                {i}. {rec.get('name', '推荐方案')}
            </h3>
            <p style='margin-bottom: 8px;'>{rec.get('description', '')}</p>
            <div style='display: flex; gap: 10px; margin-bottom: 8px;'>
                <span style='background: #E3F2FD; color: #1976D2; padding: 4px 8px;
                           border-radius: 12px; font-size: 0.9em;'>
                    评分: {rec.get('score', 0):.1f}/5.0
                </span>
                <span style='background: #F3E5F5; color: #7B1FA2; padding: 4px 8px;
                           border-radius: 12px; font-size: 0.9em;'>
                    {rec.get('category', '未分类')}
                </span>
            </div>
            {f"<a href='{rec.get('url', '#')}' target='_blank' style='color: #1976D2; text-decoration: none;'>🔗 了解更多</a>" if rec.get('url') else ''}
        </div>
        """)

    # 可行性评估
    feasibility_color = "#4CAF50" if result.feasibility_score > 0.7 else "#FF9800" if result.feasibility_score > 0.4 else "#F44336"
    html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #333; margin-bottom: 12px;'>📊 可行性评估</h3>
            <div style='display: flex; align-items: center; gap: 12px;'>
                <div style='flex: 1; background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;'>
                    <div style='background: {feasibility_color}; height: 100%; width: {result.feasibility_score*100}%;
                               transition: width 0.3s ease;'></div>
                </div>
                <span style='font-weight: bold; color: {feasibility_color};'>
                    {result.feasibility_score*100:.0f}%
                </span>
            </div>
            <p style='margin-top: 8px; font-size: 0.9em; color: #666;'>
                置信度: {result.confidence_level*100:.0f}%
            </p>
        </div>
    """)

    html_parts.append("</div>")
    return "".join(html_parts)

def format_developer_result(result: AnalysisResult, language: str, i18n: I18nManager) -> str:
    """格式化开发者模式的结果"""
    html_parts = []

    # 标题
    title = i18n.get_text("innovation_analysis", language)
    html_parts.append(f"""
    <div style='border: 2px solid #FF6B35; padding: 20px; border-radius: 12px;
                background: linear-gradient(135deg, #fff8f5 0%, #ffeee6 100%); margin-bottom: 20px;'>
        <h2 style='color: #D84315; margin-bottom: 16px; display: flex; align-items: center;'>
            🚀 {title}
        </h2>
    """)

    # 市场机会分析
    market_opportunity = result.innovation_assessment.get("market_opportunity", {})
    if market_opportunity:
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;
                    border-left: 4px solid #FF6B35;'>
            <h3 style='color: #D84315; margin-bottom: 12px;'>💡 市场机会</h3>
            <p><strong>机会等级:</strong> {market_opportunity.get('level', '中等')}</p>
            <p><strong>市场空白:</strong> {market_opportunity.get('gap_analysis', '需要进一步分析')}</p>
            <p><strong>竞争强度:</strong> {market_opportunity.get('competition_intensity', '中等')}</p>
        </div>
        """)

    # 技术可行性
    technical_feasibility = result.innovation_assessment.get("technical_feasibility", {})
    if technical_feasibility:
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #1976D2; margin-bottom: 12px;'>⚙️ 技术可行性</h3>
            <p><strong>技术难度:</strong> {technical_feasibility.get('difficulty', '中等')}</p>
            <p><strong>所需技术栈:</strong> {', '.join(technical_feasibility.get('tech_stack', []))}</p>
            <p><strong>开发周期估算:</strong> {technical_feasibility.get('timeline', '待评估')}</p>
        </div>
        """)

    # 竞品分析
    competitors = result.ecosystem_scan.get("competitors", [])[:3]
    if competitors:
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #7B1FA2; margin-bottom: 12px;'>🏢 主要竞品</h3>
        """)

        for comp in competitors:
            html_parts.append(f"""
            <div style='background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 8px;'>
                <strong>{comp.get('name', '未知产品')}</strong>
                <p style='margin: 4px 0; font-size: 0.9em;'>{comp.get('description', '')}</p>
                <div style='display: flex; gap: 8px; margin-top: 8px;'>
                    <span style='background: #E1F5FE; color: #0277BD; padding: 2px 6px;
                               border-radius: 8px; font-size: 0.8em;'>
                        {comp.get('category', '未分类')}
                    </span>
                    <span style='background: #F3E5F5; color: #7B1FA2; padding: 2px 6px;
                               border-radius: 8px; font-size: 0.8em;'>
                        用户: {comp.get('user_count', '未知')}
                    </span>
                </div>
            </div>
            """)

        html_parts.append("</div>")

    html_parts.append("</div>")
    return "".join(html_parts)

def format_market_analysis_result(market_analysis: Dict[str, Any], language: str) -> str:
    """格式化市场分析结果"""
    if "error" in market_analysis:
        return f"""
        <div style='border: 2px solid #F44336; padding: 20px; border-radius: 12px;
                    background: #FFEBEE; margin-bottom: 20px;'>
            <h2 style='color: #C62828; margin-bottom: 16px;'>❌ 市场分析失败</h2>
            <p>{market_analysis.get('message', '未知错误')}</p>
        </div>
        """

    html_parts = []

    # 标题
    html_parts.append(f"""
    <div style='border: 2px solid #FF6B35; padding: 20px; border-radius: 12px;
                background: linear-gradient(135deg, #fff8f5 0%, #ffeee6 100%); margin-bottom: 20px;'>
        <h2 style='color: #D84315; margin-bottom: 16px; display: flex; align-items: center;'>
            📊 市场分析报告
        </h2>
    """)

    # 市场机会分析
    market_opportunity = market_analysis.get("market_opportunity", {})
    if market_opportunity:
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;
                    border-left: 4px solid #FF6B35;'>
            <h3 style='color: #D84315; margin-bottom: 12px;'>💡 市场机会</h3>
            <p><strong>机会等级:</strong> {market_opportunity.get('level', '中等')}</p>
            <p><strong>市场规模:</strong> {market_opportunity.get('market_size', '待评估')}</p>
            <p><strong>增长潜力:</strong> {market_opportunity.get('growth_potential', '中等')}</p>
            <p><strong>市场空白:</strong> {market_opportunity.get('gap_analysis', '需要进一步分析')}</p>
        </div>
        """)

    # 技术可行性
    technical_feasibility = market_analysis.get("technical_feasibility", {})
    if technical_feasibility:
        tech_stack = technical_feasibility.get('tech_stack', [])
        tech_stack_str = ', '.join(tech_stack) if tech_stack else '待评估'

        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #1976D2; margin-bottom: 12px;'>⚙️ 技术可行性</h3>
            <p><strong>技术难度:</strong> {technical_feasibility.get('difficulty', '中等')}</p>
            <p><strong>所需技术栈:</strong> {tech_stack_str}</p>
            <p><strong>开发周期估算:</strong> {technical_feasibility.get('timeline', '待评估')}</p>
            <p><strong>团队规模需求:</strong> {technical_feasibility.get('team_size', '待评估')}</p>
        </div>
        """)

    # 资源需求
    resource_requirements = market_analysis.get("resource_requirements", {})
    if resource_requirements:
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #7B1FA2; margin-bottom: 12px;'>💰 资源需求</h3>
            <p><strong>资金需求:</strong> {resource_requirements.get('funding_requirement', '待评估')}</p>
            <p><strong>团队规模:</strong> {resource_requirements.get('team_size', '待评估')}</p>
            <p><strong>开发周期:</strong> {resource_requirements.get('timeline', '待评估')}</p>
        </div>
        """)

    # 风险评估
    risk_factors = market_analysis.get("risk_factors", {})
    if risk_factors:
        risks = risk_factors.get('identified_risks', [])
        risk_list = '<br>'.join([f"• {risk}" for risk in risks]) if risks else "暂无识别到的风险"

        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #F57C00; margin-bottom: 12px;'>⚠️ 风险评估</h3>
            <p><strong>风险等级:</strong> {risk_factors.get('risk_level', '中等')}</p>
            <p><strong>主要风险:</strong></p>
            <div style='margin-left: 16px; color: #666;'>{risk_list}</div>
        </div>
        """)

    # 开发者建议
    developer_recommendations = market_analysis.get("developer_recommendations", [])
    if developer_recommendations:
        html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #2E7D32; margin-bottom: 12px;'>🚀 开发建议</h3>
        """)

        for i, rec in enumerate(developer_recommendations[:3], 1):
            next_steps = rec.get('next_steps', [])
            next_steps_str = '<br>'.join([f"• {step}" for step in next_steps]) if next_steps else "待制定"

            html_parts.append(f"""
            <div style='background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 12px;'>
                <h4 style='color: #2E7D32; margin-bottom: 8px;'>{i}. {rec.get('name', '建议方案')}</h4>
                <p style='margin-bottom: 8px;'>{rec.get('description', '')}</p>
                <p><strong>市场潜力:</strong> {rec.get('market_potential', '中等')}</p>
                <p><strong>技术难度:</strong> {rec.get('technical_difficulty', '中等')}</p>
                <p><strong>下一步行动:</strong></p>
                <div style='margin-left: 16px; color: #666; font-size: 0.9em;'>{next_steps_str}</div>
            </div>
            """)

        html_parts.append("</div>")

    # 综合评分
    overall_score = market_analysis.get("overall_score", 0.5)
    score_color = "#4CAF50" if overall_score > 0.7 else "#FF9800" if overall_score > 0.4 else "#F44336"

    html_parts.append(f"""
        <div style='background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px;'>
            <h3 style='color: #333; margin-bottom: 12px;'>📈 综合评分</h3>
            <div style='display: flex; align-items: center; gap: 12px;'>
                <div style='flex: 1; background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;'>
                    <div style='background: {score_color}; height: 100%; width: {overall_score*100}%;
                               transition: width 0.3s ease;'></div>
                </div>
                <span style='font-weight: bold; color: {score_color}; font-size: 1.2em;'>
                    {overall_score*100:.0f}%
                </span>
            </div>
            <p style='margin-top: 8px; font-size: 0.9em; color: #666;'>
                基于市场机会、技术可行性、资源需求和风险因素的综合评估
            </p>
        </div>
    """)

    html_parts.append("</div>")
    return "".join(html_parts)

def create_gradio_interface():
    """创建Gradio界面"""
    app = get_app_instance()

    # 自定义CSS样式
    custom_css = """
    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .header-section {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
    }
    .mode-selector {
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    .example-buttons {
        margin: 15px 0;
    }
    .status-bar {
        margin-top: 10px;
        padding: 10px;
        background: #e3f2fd;
        border-radius: 5px;
        font-size: 0.9em;
    }
    """

    with gr.Blocks(
        title="逛逛 (InsightPulse) - 需求探索与可行性分析平台",
        theme=gr.themes.Soft(),
        css=custom_css
    ) as demo:

        # 应用状态
        language_state = gr.State("zh")

        # 头部区域
        with gr.Row(elem_classes="header-section"):
            gr.HTML("""
            <div style='text-align: center;'>
                <h1 style='margin-bottom: 10px; font-size: 2.5em;'>🔍 逛逛 (InsightPulse)</h1>
                <h2 style='margin-bottom: 15px; font-size: 1.3em; opacity: 0.9;'>需求探索与可行性分析平台</h2>
                <p style='font-size: 1.1em; opacity: 0.8; max-width: 800px; margin: 0 auto;'>
                    连接潜在用户需求与可行的解决方案，赋能创新者、开发者及普通用户，
                    帮助识别市场机会、评估创意可行性、规避重复建设
                </p>
            </div>
            """)

        # 语言切换
        with gr.Row():
            with gr.Column(scale=1):
                language_selector = gr.Radio(
                    choices=[("中文", "zh"), ("English", "en")],
                    value="zh",
                    label="语言 / Language",
                    interactive=True
                )
            with gr.Column(scale=3):
                pass

        # 功能介绍
        with gr.Row(elem_classes="mode-selector"):
            with gr.Column():
                gr.HTML("""
                <div style='text-align: center; padding: 20px; background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
                           border-radius: 12px; margin-bottom: 20px;'>
                    <h3 style='color: #1976D2; margin-bottom: 16px;'>🎯 智能需求分析平台</h3>
                    <div style='display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;'>
                        <div style='flex: 1; min-width: 200px; background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                            <h4 style='color: #4CAF50; margin-bottom: 8px;'>🔍 需求分析</h4>
                            <p style='font-size: 0.9em; color: #666; margin: 0;'>智能理解您的需求，推荐最佳解决方案</p>
                        </div>
                        <div style='flex: 1; min-width: 200px; background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                            <h4 style='color: #FF9800; margin-bottom: 8px;'>📊 市场分析</h4>
                            <p style='font-size: 0.9em; color: #666; margin: 0;'>为产品经理和开发者提供专业的市场洞察</p>
                        </div>
                        <div style='flex: 1; min-width: 200px; background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                            <h4 style='color: #9C27B0; margin-bottom: 8px;'>💡 决策支持</h4>
                            <p style='font-size: 0.9em; color: #666; margin: 0;'>基于数据的可行性评估和建议</p>
                        </div>
                    </div>
                </div>
                """)

        # 主要交互区域
        with gr.Row():
            with gr.Column(scale=4):
                # 聊天界面
                chatbot = gr.Chatbot(
                    height=500,
                    show_label=False,
                    elem_id="chatbot",
                    type="messages",
                    placeholder="在这里查看分析结果..."
                )

                # 输入区域
                with gr.Row():
                    user_input = gr.Textbox(
                        show_label=False,
                        placeholder="请描述您的需求，例如：有什么好用的笔记软件？",
                        container=False,
                        scale=4
                    )
                    submit_btn = gr.Button("🔍 分析", variant="primary", scale=1)

                # 市场分析按钮
                with gr.Row():
                    market_analysis_btn = gr.Button(
                        "📊 市场分析 (为产品经理和开发者)",
                        variant="secondary",
                        size="lg",
                        visible=True
                    )

                # 示例问题
                with gr.Row(elem_classes="example-buttons"):
                    example_1 = gr.Button("📝 有什么好用的笔记软件？", size="sm")
                    example_2 = gr.Button("🏃‍♂️ 推荐一些健身APP", size="sm")
                    example_3 = gr.Button("💡 AI写作助手有哪些？", size="sm")
                    example_4 = gr.Button("🔧 任务管理工具推荐", size="sm")

                # 状态栏
                status_display = gr.HTML(
                    "<div class='status-bar'>💡 提示：输入您的需求开始分析，分析完成后可点击市场分析按钮获取专业建议</div>"
                )

            with gr.Column(scale=1):
                # 功能说明
                gr.HTML("""
                <div style='padding: 20px; background: #f8f9fa; border-radius: 10px; margin-bottom: 20px;'>
                    <h3 style='color: #333; margin-bottom: 15px;'>🌟 核心功能</h3>
                    <ul style='color: #666; line-height: 1.6;'>
                        <li>🔍 智能需求解析</li>
                        <li>🌐 生态扫描分析</li>
                        <li>📊 竞品对比评估</li>
                        <li>💡 创新机会识别</li>
                        <li>⚡ 可行性评分</li>
                        <li>📋 决策建议生成</li>
                    </ul>
                </div>
                """)

                # 清除按钮
                clear_btn = gr.Button("🗑️ 清除历史", variant="secondary")

                # 导出功能（预留）
                gr.HTML("""
                <div style='padding: 15px; background: #e8f5e8; border-radius: 8px; margin-top: 20px;'>
                    <h4 style='color: #2e7d32; margin-bottom: 10px;'>📄 导出功能</h4>
                    <p style='color: #666; font-size: 0.9em;'>分析报告导出功能即将上线</p>
                </div>
                """)

        # 事件处理函数
        def sync_process_user_input(user_input_text, language_value, chatbot_history):
            """同步包装异步函数"""
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(
                    process_user_input(user_input_text, language_value, chatbot_history)
                )
                loop.close()
                return result
            except Exception as e:
                logger.error(f"处理用户输入时发生错误: {str(e)}")
                error_chatbot = chatbot_history + [{
                    "role": "assistant",
                    "content": f"❌ 处理请求时发生错误: {str(e)}"
                }]
                return error_chatbot, f"错误: {str(e)}"

        def sync_process_market_analysis(language_value, chatbot_history):
            """同步包装市场分析异步函数"""
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(
                    process_market_analysis(language_value, chatbot_history)
                )
                loop.close()
                return result
            except Exception as e:
                logger.error(f"处理市场分析时发生错误: {str(e)}")
                error_chatbot = chatbot_history + [{
                    "role": "assistant",
                    "content": f"❌ 市场分析失败: {str(e)}"
                }]
                return error_chatbot, f"错误: {str(e)}"

        # 绑定事件
        submit_btn.click(
            sync_process_user_input,
            inputs=[user_input, language_state, chatbot],
            outputs=[chatbot, status_display]
        ).then(
            lambda: "",
            outputs=[user_input]
        )

        user_input.submit(
            sync_process_user_input,
            inputs=[user_input, language_state, chatbot],
            outputs=[chatbot, status_display]
        ).then(
            lambda: "",
            outputs=[user_input]
        )

        # 市场分析按钮事件
        market_analysis_btn.click(
            sync_process_market_analysis,
            inputs=[language_state, chatbot],
            outputs=[chatbot, status_display]
        )

        # 示例问题点击事件
        example_1.click(lambda: "有什么好用的笔记软件？", outputs=[user_input])
        example_2.click(lambda: "推荐一些健身APP", outputs=[user_input])
        example_3.click(lambda: "AI写作助手有哪些？", outputs=[user_input])
        example_4.click(lambda: "任务管理工具推荐", outputs=[user_input])

        # 清除历史
        clear_btn.click(lambda: ([], "<div class='status-bar'>✅ 历史记录已清除</div>"), outputs=[chatbot, status_display])

        # 语言切换
        language_selector.change(
            lambda lang: lang,
            inputs=[language_selector],
            outputs=[language_state]
        )

    return demo

def main():
    """主函数"""
    try:
        logger.info("启动逛逛 (InsightPulse) 应用...")

        # 创建Gradio界面
        demo = create_gradio_interface()

        # 启动应用
        demo.queue(max_size=20)
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            show_error=True,
            show_tips=True,
            enable_queue=True
        )

    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        raise

if __name__ == "__main__":
    main()
