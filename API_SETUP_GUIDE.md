# API密钥配置指南

## 🎯 概述

逛逛 (InsightPulse) 支持多个AI服务提供商，您可以根据需要选择配置：

- **DeepSeek**: 性价比高，中文支持好，推理能力强 (推荐)
- **OpenAI**: 功能强大，生态完善
- **Claude**: 安全性高，长文本处理能力强
- **OpenAI兼容服务**: 第三方服务商，通常更便宜

## 🔧 配置步骤

### 1. 编辑 .env 文件

复制 `.env.example` 为 `.env`：
```bash
cp .env.example .env
```

### 2. 选择并配置API提供商

#### 选项1: DeepSeek (推荐新手)
```bash
# 在 .env 文件中设置
DEEPSEEK_API_KEY=sk-your-deepseek-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
```

**获取方法:**
1. 访问 https://platform.deepseek.com/
2. 注册账户并登录
3. 进入 API Keys 页面
4. 点击"创建新密钥"
5. 复制密钥到 .env 文件

#### 选项2: OpenAI 官方
```bash
# 在 .env 文件中设置
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
```

**获取方法:**
1. 访问 https://platform.openai.com/api-keys
2. 登录或注册账户
3. 点击 "Create new secret key"
4. 复制密钥到 .env 文件

#### 选项3: Claude (Anthropic)
```bash
# 在 .env 文件中设置
CLAUDE_API_KEY=sk-ant-your-claude-api-key
CLAUDE_BASE_URL=https://api.anthropic.com
```

**获取方法:**
1. 访问 https://console.anthropic.com/
2. 注册并登录
3. 进入 API Keys 页面
4. 创建新密钥
5. 复制密钥到 .env 文件

#### 选项4: OpenAI兼容服务
```bash
# 在 .env 文件中设置
OPENAI_COMPATIBLE_API_KEY=your-compatible-api-key
OPENAI_COMPATIBLE_BASE_URL=https://your-provider.com/v1
```

**常见兼容服务:**
- 各种国内AI服务商
- 自部署的模型服务
- 代理服务

### 3. 配置搜索服务 (可选)

```bash
# Tavily搜索API
TAVILY_API_KEY=tvly-your-tavily-api-key
```

**获取方法:**
1. 访问 https://tavily.com/
2. 注册账户
3. 进入 Dashboard
4. 复制 API Key

## 📋 配置示例

### 完整配置示例
```bash
# DeepSeek (主要AI服务)
DEEPSEEK_API_KEY=sk-1234567890abcdef
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# OpenAI (备用AI服务)
OPENAI_API_KEY=sk-abcdef1234567890
OPENAI_BASE_URL=https://api.openai.com/v1

# 搜索服务
TAVILY_API_KEY=tvly-1234567890abcdef

# 应用配置
DEBUG=false
LOG_LEVEL=INFO
```

### 最小配置示例
```bash
# 只配置一个AI服务即可
DEEPSEEK_API_KEY=sk-your-deepseek-key

# 其他保持空白即可
CLAUDE_API_KEY=
OPENAI_API_KEY=
OPENAI_COMPATIBLE_API_KEY=
TAVILY_API_KEY=
```

## ✅ 验证配置

启动应用后，查看右侧的"系统状态"面板：

- **🤖 AI功能**: 显示已配置的提供商
- **🔍 搜索功能**: 显示搜索服务状态

## 💡 使用建议

### 成本优化
1. **DeepSeek**: 最便宜，适合大量使用
2. **OpenAI兼容服务**: 通常比官方便宜
3. **OpenAI官方**: 功能最全但较贵
4. **Claude**: 适合对安全性要求高的场景

### 功能选择
- **日常使用**: DeepSeek + Tavily
- **专业分析**: OpenAI + Tavily  
- **安全场景**: Claude + Tavily
- **预算有限**: 只配置DeepSeek

### 多提供商配置
- 应用会自动选择第一个可用的提供商
- 可以配置多个作为备用
- 不同提供商有不同的特点和限制

## 🔒 安全提示

1. **保护API密钥**: 不要将 .env 文件提交到版本控制
2. **定期轮换**: 定期更换API密钥
3. **监控使用**: 关注API使用量和费用
4. **权限控制**: 使用最小权限原则

## ❓ 常见问题

### Q: 可以不配置任何API密钥吗？
A: 可以，应用会启动但AI功能不可用，只能查看界面

### Q: 配置多个提供商有什么好处？
A: 提供备用选择，避免单点故障，可以根据需要切换

### Q: 哪个提供商最推荐？
A: DeepSeek性价比最高，适合大多数用户

### Q: API密钥安全吗？
A: 密钥只在本地使用，不会上传到任何服务器

---

**配置完成后，重启应用即可享受完整功能！** 🚀
