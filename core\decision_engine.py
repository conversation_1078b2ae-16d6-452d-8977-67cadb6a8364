#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decision Engine Module
决策建议引擎 - 负责生成个性化的决策建议
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class Recommendation:
    """推荐项"""
    name: str
    description: str
    category: str
    score: float
    pros: List[str]
    cons: List[str]
    url: str
    pricing: str
    target_users: List[str]

class DecisionEngine:
    """决策建议引擎"""
    
    def __init__(self, llm_interface):
        """初始化决策引擎"""
        self.llm = llm_interface
        
        # 推荐策略配置
        self.recommendation_strategies = {
            "regular": {
                "focus": "existing_solutions",
                "criteria": ["ease_of_use", "popularity", "cost_effectiveness"],
                "max_recommendations": 5
            },
            "developer": {
                "focus": "innovation_opportunities", 
                "criteria": ["market_potential", "technical_feasibility", "differentiation"],
                "max_recommendations": 3
            }
        }
        
        logger.info("决策引擎初始化完成")
    
    async def generate_recommendations(
        self,
        demand_analysis: Dict[str, Any],
        ecosystem_scan: Dict[str, Any],
        innovation_assessment: Dict[str, Any],
        user_mode: str,
        language: str = "zh"
    ) -> List[Dict[str, Any]]:
        """
        生成决策建议
        
        Args:
            demand_analysis: 需求分析结果
            ecosystem_scan: 生态扫描结果
            innovation_assessment: 创新评估结果
            user_mode: 用户模式 (regular/developer)
            language: 语言
            
        Returns:
            推荐列表
        """
        try:
            logger.info(f"开始生成决策建议 (模式: {user_mode})...")

            if user_mode == "regular":
                recommendations = await self._generate_regular_user_recommendations(
                    demand_analysis, ecosystem_scan, language
                )
            else:
                recommendations = await self._generate_developer_recommendations(
                    demand_analysis, ecosystem_scan, innovation_assessment, language
                )
            
            # 排序和过滤推荐
            filtered_recommendations = self._filter_and_rank_recommendations(
                recommendations, user_mode
            )
            
            logger.info(f"生成了 {len(filtered_recommendations)} 个推荐")
            return filtered_recommendations
            
        except Exception as e:
            logger.error(f"决策建议生成失败: {str(e)}")
            return self._create_fallback_recommendations(demand_analysis, user_mode)
    
    async def _generate_regular_user_recommendations(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any], 
        language: str
    ) -> List[Dict[str, Any]]:
        """为普通用户生成推荐"""
        
        # 基于竞品分析生成推荐
        competitors = ecosystem_scan.get("competitors", [])
        
        # 构建推荐生成提示词
        prompt = self._build_regular_user_prompt(demand_analysis, competitors, language)
        
        try:
            response = await self.llm.generate_analysis(prompt)
            result = await self.llm.parse_json_response(response.content)
            
            recommendations = []
            for rec_data in result.get("recommendations", []):
                recommendation = {
                    "name": rec_data.get("name", "推荐方案"),
                    "description": rec_data.get("description", ""),
                    "category": rec_data.get("category", "未分类"),
                    "score": float(rec_data.get("score", 0.0)),
                    "pros": rec_data.get("pros", []),
                    "cons": rec_data.get("cons", []),
                    "url": rec_data.get("url", ""),
                    "pricing": rec_data.get("pricing", "未知"),
                    "target_users": rec_data.get("target_users", []),
                    "ease_of_use": rec_data.get("ease_of_use", "中等"),
                    "popularity": rec_data.get("popularity", "中等"),
                    "recommendation_reason": rec_data.get("recommendation_reason", "")
                }
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            logger.warning(f"普通用户推荐生成失败: {str(e)}")
            return self._extract_basic_recommendations_from_competitors(competitors)
    
    async def _generate_developer_recommendations(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any], 
        innovation_assessment: Dict[str, Any], 
        language: str
    ) -> List[Dict[str, Any]]:
        """为开发者生成推荐"""
        
        # 构建开发者推荐提示词
        prompt = self._build_developer_prompt(
            demand_analysis, ecosystem_scan, innovation_assessment, language
        )
        
        try:
            response = await self.llm.generate_analysis(prompt)
            result = await self.llm.parse_json_response(response.content)
            
            recommendations = []
            for rec_data in result.get("recommendations", []):
                recommendation = {
                    "name": rec_data.get("name", "创新机会"),
                    "description": rec_data.get("description", ""),
                    "category": rec_data.get("category", "创新项目"),
                    "score": float(rec_data.get("score", 0.0)),
                    "pros": rec_data.get("pros", []),
                    "cons": rec_data.get("cons", []),
                    "url": rec_data.get("url", ""),
                    "market_potential": rec_data.get("market_potential", "中等"),
                    "technical_difficulty": rec_data.get("technical_difficulty", "中等"),
                    "investment_required": rec_data.get("investment_required", "中等"),
                    "time_to_market": rec_data.get("time_to_market", "中期"),
                    "differentiation_strategy": rec_data.get("differentiation_strategy", ""),
                    "target_market": rec_data.get("target_market", []),
                    "next_steps": rec_data.get("next_steps", [])
                }
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            logger.warning(f"开发者推荐生成失败: {str(e)}")
            return self._create_basic_developer_recommendations(innovation_assessment)
    
    def _build_regular_user_prompt(
        self, 
        demand_analysis: Dict[str, Any], 
        competitors: List[Dict[str, Any]], 
        language: str
    ) -> str:
        """构建普通用户推荐提示词"""
        
        competitors_info = ""
        for comp in competitors[:8]:
            competitors_info += f"- {comp.get('name', '')}\n"
            competitors_info += f"  描述: {comp.get('description', '')}\n"
            competitors_info += f"  特性: {', '.join(comp.get('features', [])[:3])}\n"
            competitors_info += f"  价格: {comp.get('pricing', '未知')}\n"
            competitors_info += f"  评分: {comp.get('rating', 0)}\n\n"
        
        if language == "zh":
            return f"""作为产品推荐专家，请为普通用户推荐最适合的解决方案。

用户需求：{demand_analysis.get('summary', '')}
需求关键词：{', '.join(demand_analysis.get('keywords', []))}
约束条件：{', '.join(demand_analysis.get('constraints', []))}

可选产品/服务：
{competitors_info}

请按JSON格式返回推荐：

{{
    "recommendations": [
        {{
            "name": "产品名称",
            "description": "产品描述",
            "category": "产品分类",
            "score": 4.5,
            "pros": ["优势1", "优势2"],
            "cons": ["劣势1", "劣势2"],
            "url": "产品链接",
            "pricing": "价格信息",
            "target_users": ["目标用户"],
            "ease_of_use": "易用性（简单/中等/复杂）",
            "popularity": "受欢迎程度（高/中/低）",
            "recommendation_reason": "推荐理由"
        }}
    ]
}}

推荐标准：
1. 与用户需求的匹配度
2. 产品质量和用户评价
3. 易用性和学习成本
4. 性价比
5. 用户群体和口碑

请推荐3-5个最佳选择，按推荐度排序。"""

        else:  # English
            return f"""As a product recommendation expert, please recommend the most suitable solutions for regular users.

User requirement: {demand_analysis.get('summary', '')}
Requirement keywords: {', '.join(demand_analysis.get('keywords', []))}
Constraints: {', '.join(demand_analysis.get('constraints', []))}

Available products/services:
{competitors_info}

Please return recommendations in JSON format:

{{
    "recommendations": [
        {{
            "name": "Product name",
            "description": "Product description",
            "category": "Product category",
            "score": 4.5,
            "pros": ["Advantage 1", "Advantage 2"],
            "cons": ["Disadvantage 1", "Disadvantage 2"],
            "url": "Product link",
            "pricing": "Pricing information",
            "target_users": ["Target users"],
            "ease_of_use": "Ease of use (Simple/Medium/Complex)",
            "popularity": "Popularity (High/Medium/Low)",
            "recommendation_reason": "Recommendation reason"
        }}
    ]
}}

Recommendation criteria:
1. Match with user requirements
2. Product quality and user reviews
3. Ease of use and learning cost
4. Cost-effectiveness
5. User base and reputation

Please recommend 3-5 best choices, sorted by recommendation level."""
    
    def _build_developer_prompt(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any], 
        innovation_assessment: Dict[str, Any], 
        language: str
    ) -> str:
        """构建开发者推荐提示词"""
        
        market_opportunity = innovation_assessment.get("market_opportunity", {})
        technical_feasibility = innovation_assessment.get("technical_feasibility", {})
        competitive_analysis = innovation_assessment.get("competitive_analysis", {})
        
        if language == "zh":
            return f"""作为创新顾问，请为开发者推荐最佳的创新机会和行动方案。

用户需求：{demand_analysis.get('summary', '')}
市场机会等级：{market_opportunity.get('level', '中等')}
技术难度：{technical_feasibility.get('difficulty', '中等')}
竞争强度：{competitive_analysis.get('competition_intensity', '中等')}
市场成熟度：{ecosystem_scan.get('market_maturity', 0.5)}

请按JSON格式返回创新建议：

{{
    "recommendations": [
        {{
            "name": "创新方向名称",
            "description": "详细描述",
            "category": "创新类型",
            "score": 4.2,
            "pros": ["优势"],
            "cons": ["挑战"],
            "market_potential": "市场潜力（高/中/低）",
            "technical_difficulty": "技术难度（高/中/低）",
            "investment_required": "投资需求（高/中/低）",
            "time_to_market": "上市时间（短期/中期/长期）",
            "differentiation_strategy": "差异化策略",
            "target_market": ["目标市场"],
            "next_steps": ["下一步行动"]
        }}
    ]
}}

评估维度：
1. 市场机会大小
2. 技术可行性
3. 竞争优势
4. 资源要求
5. 风险回报比

请推荐2-3个最有潜力的方向。"""

        else:  # English
            return f"""As an innovation consultant, please recommend the best innovation opportunities and action plans for developers.

User requirement: {demand_analysis.get('summary', '')}
Market opportunity level: {market_opportunity.get('level', 'Medium')}
Technical difficulty: {technical_feasibility.get('difficulty', 'Medium')}
Competition intensity: {competitive_analysis.get('competition_intensity', 'Medium')}
Market maturity: {ecosystem_scan.get('market_maturity', 0.5)}

Please return innovation recommendations in JSON format:

{{
    "recommendations": [
        {{
            "name": "Innovation direction name",
            "description": "Detailed description",
            "category": "Innovation type",
            "score": 4.2,
            "pros": ["Advantages"],
            "cons": ["Challenges"],
            "market_potential": "Market potential (High/Medium/Low)",
            "technical_difficulty": "Technical difficulty (High/Medium/Low)",
            "investment_required": "Investment required (High/Medium/Low)",
            "time_to_market": "Time to market (Short/Medium/Long term)",
            "differentiation_strategy": "Differentiation strategy",
            "target_market": ["Target markets"],
            "next_steps": ["Next action steps"]
        }}
    ]
}}

Evaluation dimensions:
1. Market opportunity size
2. Technical feasibility
3. Competitive advantage
4. Resource requirements
5. Risk-return ratio

Please recommend 2-3 most promising directions."""
    
    def _filter_and_rank_recommendations(
        self, 
        recommendations: List[Dict[str, Any]], 
        user_mode
    ) -> List[Dict[str, Any]]:
        """过滤和排序推荐"""
        
        if not recommendations:
            return []
        
        # 按评分排序
        sorted_recommendations = sorted(
            recommendations, 
            key=lambda x: x.get("score", 0), 
            reverse=True
        )
        
        # 根据用户模式限制数量
        max_count = self.recommendation_strategies[user_mode]["max_recommendations"]
        
        return sorted_recommendations[:max_count]
    
    def _extract_basic_recommendations_from_competitors(
        self, 
        competitors: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """从竞品中提取基础推荐"""
        recommendations = []
        
        for comp in competitors[:5]:
            recommendation = {
                "name": comp.get("name", "推荐产品"),
                "description": comp.get("description", ""),
                "category": comp.get("category", "未分类"),
                "score": comp.get("rating", 0.0),
                "pros": comp.get("pros", []),
                "cons": comp.get("cons", []),
                "url": comp.get("url", ""),
                "pricing": comp.get("pricing", "未知"),
                "target_users": comp.get("target_users", []),
                "ease_of_use": "中等",
                "popularity": "中等",
                "recommendation_reason": "基于市场分析推荐"
            }
            recommendations.append(recommendation)
        
        return recommendations
    
    def _create_basic_developer_recommendations(
        self, 
        innovation_assessment: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """创建基础开发者推荐"""
        
        overall_score = innovation_assessment.get("overall_score", 0.5)
        
        if overall_score > 0.7:
            recommendation_type = "高潜力创新机会"
            description = "市场机会大，技术可行性高，建议优先考虑"
        elif overall_score > 0.4:
            recommendation_type = "中等创新机会"
            description = "有一定市场潜力，需要进一步验证"
        else:
            recommendation_type = "谨慎评估"
            description = "市场风险较高，建议深入调研后决策"
        
        return [{
            "name": recommendation_type,
            "description": description,
            "category": "创新评估",
            "score": overall_score * 5,
            "pros": ["基于综合分析"],
            "cons": ["需要进一步验证"],
            "market_potential": "待评估",
            "technical_difficulty": "待评估",
            "investment_required": "待评估",
            "time_to_market": "待评估",
            "differentiation_strategy": "待制定",
            "target_market": [],
            "next_steps": ["深入市场调研", "技术可行性验证", "商业模式设计"]
        }]
    
    def _create_fallback_recommendations(
        self,
        demand_analysis: Dict[str, Any],
        user_mode: str
    ) -> List[Dict[str, Any]]:
        """创建备用推荐"""
        
        if user_mode == "regular":
            return [{
                "name": "需要进一步搜索",
                "description": f"针对您的需求 '{demand_analysis.get('summary', '')}' 需要更多信息",
                "category": "搜索建议",
                "score": 3.0,
                "pros": ["个性化搜索"],
                "cons": ["需要手动搜索"],
                "url": "",
                "pricing": "免费",
                "target_users": ["所有用户"],
                "recommendation_reason": "系统分析不足，建议手动搜索"
            }]
        else:
            return [{
                "name": "市场调研建议",
                "description": "建议进行更深入的市场调研和用户访谈",
                "category": "调研建议",
                "score": 3.0,
                "pros": ["降低风险"],
                "cons": ["需要时间投入"],
                "market_potential": "待评估",
                "technical_difficulty": "待评估",
                "next_steps": ["用户访谈", "竞品深度分析", "技术原型验证"]
            }]
