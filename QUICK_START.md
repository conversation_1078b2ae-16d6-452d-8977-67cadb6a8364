# 逛逛 (InsightPulse) 快速启动指南

## 🚀 快速开始

### 1. 设置API密钥 (可选)

编辑 `.env` 文件，设置您的API密钥。**支持多个提供商，不强制填满**：

```bash
# LLM API配置 - 任选其一或多个
DEEPSEEK_API_KEY=your_deepseek_api_key
CLAUDE_API_KEY=your_claude_api_key
OPENAI_API_KEY=your_openai_api_key
OPENAI_COMPATIBLE_API_KEY=your_compatible_api_key

# 搜索API密钥 (可选)
TAVILY_API_KEY=your_tavily_api_key
```

**💡 重要提示：**
- 未设置API密钥也可以启动应用查看界面
- 应用会显示配置状态和功能可用性
- 至少配置一个LLM API密钥以启用AI功能

### 2. 启动应用

```bash
# 方法1: 使用启动脚本 (推荐)
python start.py

# 方法2: 直接运行
python app.py
```

### 3. 访问界面

打开浏览器访问: http://localhost:7860

## 🎯 主要功能

### 统一入口设计
- **单一分析入口**: 输入任何需求，获得智能推荐
- **追问式市场分析**: 分析完成后，点击"📊 市场分析"按钮获取专业的产品经理和开发者视角

### 使用流程

1. **输入需求**: 在文本框中描述您的需求
   - 例如："有什么好用的笔记软件？"
   - 例如："AI写作助手有哪些？"

2. **获得推荐**: 系统会提供：
   - 需求理解分析
   - TOP推荐产品/服务
   - 功能对比和评分
   - 可行性评估

3. **深度分析** (可选): 点击"📊 市场分析"按钮获取：
   - 市场机会分析
   - 技术可行性评估
   - 竞争格局分析
   - 资源需求评估
   - 开发建议和下一步行动

## 🔧 API密钥获取

### LLM API密钥 (任选其一或多个)

#### 1. DeepSeek (推荐)
- 网站: https://platform.deepseek.com/
- 特点: 性价比高，支持中文，推理能力强
- 获取: 注册 → API Keys → 创建新密钥

#### 2. OpenAI (官方)
- 网站: https://platform.openai.com/api-keys
- 特点: 功能强大，生态完善
- 获取: 注册 → API Keys → Create new secret key

#### 3. Claude (Anthropic)
- 网站: https://console.anthropic.com/
- 特点: 安全性高，长文本处理能力强
- 获取: 注册 → API Keys → Create Key

#### 4. OpenAI兼容服务
- 各种第三方服务商提供的OpenAI兼容API
- 通常价格更便宜，速度更快

### Tavily搜索API (可选)
- 网站: https://tavily.com/
- 特点: 专业的AI搜索服务
- 获取: 注册 → Dashboard → API Keys

## ❓ 常见问题

### Q: 启动时提示API密钥错误
A: 请检查 `.env` 文件中的API密钥是否正确设置

### Q: 搜索功能不工作
A: 请确认Tavily API密钥已正确设置

### Q: 如何修改端口
A: 在 `app.py` 中修改 `server_port=7860` 为其他端口

## 📞 获取帮助

- 运行 `python test_app.py` 诊断问题
- 查看 `logs/` 目录中的日志文件
- 参考 `README.md` 获取详细文档

---

**开始探索您的需求吧！** 🔍✨
