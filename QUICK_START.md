# 逛逛 (InsightPulse) 快速启动指南

## 🚀 快速开始

### 1. 设置API密钥

编辑 `.env` 文件，设置您的API密钥：

```bash
# LLM API密钥 (必需)
MODEL_API_KEY=your_openai_compatible_api_key

# 搜索API密钥 (必需)  
TAVILY_API_KEY=your_tavily_api_key

# 可选：自定义API端点
MODEL_BASE_URL=https://api.openai.com/v1
```

### 2. 启动应用

```bash
# 方法1: 使用启动脚本 (推荐)
python start.py

# 方法2: 直接运行
python app.py
```

### 3. 访问界面

打开浏览器访问: http://localhost:7860

## 🎯 主要功能

### 统一入口设计
- **单一分析入口**: 输入任何需求，获得智能推荐
- **追问式市场分析**: 分析完成后，点击"📊 市场分析"按钮获取专业的产品经理和开发者视角

### 使用流程

1. **输入需求**: 在文本框中描述您的需求
   - 例如："有什么好用的笔记软件？"
   - 例如："AI写作助手有哪些？"

2. **获得推荐**: 系统会提供：
   - 需求理解分析
   - TOP推荐产品/服务
   - 功能对比和评分
   - 可行性评估

3. **深度分析** (可选): 点击"📊 市场分析"按钮获取：
   - 市场机会分析
   - 技术可行性评估
   - 竞争格局分析
   - 资源需求评估
   - 开发建议和下一步行动

## 🔧 API密钥获取

### LLM API密钥
- **OpenAI**: https://platform.openai.com/api-keys
- **DeepSeek**: https://platform.deepseek.com/
- **其他兼容OpenAI格式的服务**

### Tavily搜索API
- 访问: https://tavily.com/
- 注册并获取API密钥

## ❓ 常见问题

### Q: 启动时提示API密钥错误
A: 请检查 `.env` 文件中的API密钥是否正确设置

### Q: 搜索功能不工作
A: 请确认Tavily API密钥已正确设置

### Q: 如何修改端口
A: 在 `app.py` 中修改 `server_port=7860` 为其他端口

## 📞 获取帮助

- 运行 `python test_app.py` 诊断问题
- 查看 `logs/` 目录中的日志文件
- 参考 `README.md` 获取详细文档

---

**开始探索您的需求吧！** 🔍✨
