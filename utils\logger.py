#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logger Module
日志管理模块
"""

import logging
import sys
import os
from datetime import datetime
from typing import Optional

def setup_logger(
    name: str = "InsightPulse",
    level: str = "INFO",
    log_file: Optional[str] = None,
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        format_string: 日志格式字符串
        
    Returns:
        配置好的日志记录器
    """
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 设置日志格式
    if format_string is None:
        format_string = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(filename)s:%(lineno)d - %(message)s"
        )
    
    formatter = logging.Formatter(format_string)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(log_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
        except Exception as e:
            logger.warning(f"无法创建日志文件 {log_file}: {str(e)}")
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(name)

class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取类的日志记录器"""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise
    return wrapper

def log_execution_time(func):
    """执行时间日志装饰器"""
    import time
    
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.2f}秒): {str(e)}")
            raise
    
    return wrapper

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_event(self, event_type: str, message: str, **kwargs):
        """记录结构化事件"""
        log_data = {
            "event_type": event_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        
        self.logger.info(f"EVENT: {log_data}")
    
    def log_user_action(self, user_id: str, action: str, details: dict = None):
        """记录用户行为"""
        self.log_event(
            "user_action",
            f"用户 {user_id} 执行操作: {action}",
            user_id=user_id,
            action=action,
            details=details or {}
        )
    
    def log_api_call(self, endpoint: str, method: str, status_code: int, response_time: float):
        """记录API调用"""
        self.log_event(
            "api_call",
            f"API调用: {method} {endpoint}",
            endpoint=endpoint,
            method=method,
            status_code=status_code,
            response_time=response_time
        )
    
    def log_error(self, error_type: str, error_message: str, **kwargs):
        """记录错误"""
        self.log_event(
            "error",
            f"错误: {error_type} - {error_message}",
            error_type=error_type,
            error_message=error_message,
            **kwargs
        )
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能指标"""
        self.log_event(
            "performance",
            f"性能: {operation} 耗时 {duration:.2f}秒",
            operation=operation,
            duration=duration,
            **kwargs
        )

def configure_app_logging(
    app_name: str = "InsightPulse",
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_dir: str = "logs"
) -> logging.Logger:
    """
    配置应用程序日志
    
    Args:
        app_name: 应用名称
        log_level: 日志级别
        log_to_file: 是否记录到文件
        log_dir: 日志目录
        
    Returns:
        配置好的主日志记录器
    """
    
    # 创建日志目录
    if log_to_file and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名
    log_file = None
    if log_to_file:
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(log_dir, f"{app_name}_{timestamp}.log")
    
    # 设置主日志记录器
    main_logger = setup_logger(
        name=app_name,
        level=log_level,
        log_file=log_file
    )
    
    # 设置第三方库的日志级别
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    main_logger.info(f"{app_name} 日志系统初始化完成")
    main_logger.info(f"日志级别: {log_level}")
    if log_file:
        main_logger.info(f"日志文件: {log_file}")
    
    return main_logger
