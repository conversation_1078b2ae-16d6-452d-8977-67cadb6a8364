# 逛逛 (InsightPulse) - 需求探索与可行性分析平台

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Gradio](https://img.shields.io/badge/Gradio-4.0+-green.svg)](https://gradio.app)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 项目概述

逛逛 (InsightPulse) 是一个智能的需求探索与可行性分析平台，旨在连接潜在用户需求与可行的解决方案。通过先进的AI技术和多源数据分析，为创新者、开发者及普通用户提供精准的市场洞察和决策支持。

### 核心价值

- **用户痛点解决**: 快速判断某项需求是否已有成熟解决方案，避免信息不对称
- **开发者赋能**: 有效识别具有商业价值的创新方向，避免"重复造轮子"
- **市场效率**: 减少社会资源在重复建设和无效创新上的浪费

## 功能特性

### 🔍 智能需求解析
- 深度理解用户输入的文本需求
- 自动提取核心意图、功能特性和约束条件
- 智能识别用户模式（普通用户/开发者）
- 需求领域自动分类

### 🌐 生态扫描分析
- 多源搜索引擎集成（Tavily等）
- 竞品信息自动收集和分析
- 市场成熟度评估
- 市场空白识别

### 💡 创新雷达评估
- 市场机会量化分析
- 技术可行性评估
- 竞争格局深度分析
- 资源需求估算
- 风险因素识别

### 📋 智能决策建议
- 双模式个性化推荐
  - **普通用户模式**: 现有解决方案推荐
  - **开发者模式**: 创新机会和行动方案
- 可行性评分和置信度计算
- 结构化决策报告

### 🌍 国际化支持
- 中英文双语界面
- 多语言内容生成
- 本地化用户体验

## 技术架构

### 核心引擎模块

```
core/
├── demand_analyzer.py      # 需求解析引擎
├── ecosystem_scanner.py    # 生态扫描引擎
├── innovation_radar.py     # 创新雷达引擎
├── decision_engine.py      # 决策建议引擎
└── llm_interface.py        # 统一LLM接口层
```

### 技术栈

- **前端**: Gradio 4.0+ (Web界面)
- **后端**: Python 3.8+
- **AI模型**: 
  - DeepSeek R1 (推理分析)
  - Qwen 2.5-72B (内容生成)
- **搜索引擎**: Tavily API
- **数据处理**: Pandas, NumPy
- **异步支持**: AsyncIO, AIOHTTP

## 快速开始

### 环境要求

- Python 3.8+
- 8GB+ RAM
- 网络连接（用于API调用）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd insightpulse
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 创建 .env 文件
MODEL_API_KEY=your_openai_api_key
MODEL_BASE_URL=your_model_base_url
TAVILY_API_KEY=your_tavily_api_key
```

4. **启动应用**
```bash
python app.py
```

5. **访问界面**
打开浏览器访问 `http://localhost:7860`

### Docker 部署

```bash
# 构建镜像
docker build -t insightpulse .

# 运行容器
docker run -p 7860:7860 \
  -e MODEL_API_KEY=your_key \
  -e TAVILY_API_KEY=your_key \
  insightpulse
```

## 使用指南

### 普通用户模式

1. 选择"普通用户模式"
2. 输入需求描述，如："有什么好用的笔记软件？"
3. 系统将推荐现有的解决方案，包括：
   - 产品名称和描述
   - 功能特性对比
   - 用户评价和评分
   - 价格信息
   - 使用建议

### 开发者模式

1. 选择"开发者模式"
2. 输入创新想法，如："开发一个AI写作助手"
3. 系统将提供：
   - 市场机会分析
   - 技术可行性评估
   - 竞品格局分析
   - 资源需求估算
   - 风险评估和建议

## 配置说明

### 配置文件 (config.json)

```json
{
  "llm": {
    "models": {
      "reasoning": "deepseek-ai/DeepSeek-R1",
      "analysis": "Qwen/Qwen2.5-72B-Instruct",
      "summary": "Qwen/Qwen2.5-72B-Instruct"
    },
    "temperature": 0.6,
    "max_tokens": 4000
  },
  "search": {
    "max_results": 10,
    "search_depth": "advanced"
  },
  "app": {
    "debug": false,
    "log_level": "INFO",
    "max_history": 100
  }
}
```

### 环境变量

| 变量名 | 描述 | 必需 |
|--------|------|------|
| `MODEL_API_KEY` | LLM API密钥 | ✅ |
| `MODEL_BASE_URL` | LLM API基础URL | ❌ |
| `TAVILY_API_KEY` | Tavily搜索API密钥 | ✅ |
| `DEBUG` | 调试模式 | ❌ |
| `LOG_LEVEL` | 日志级别 | ❌ |

## 开发指南

### 项目结构

```
insightpulse/
├── app.py                  # 主应用文件
├── core/                   # 核心引擎模块
├── utils/                  # 工具模块
├── locales/               # 国际化文件
├── logs/                  # 日志文件
├── config.json            # 配置文件
├── requirements.txt       # 依赖列表
└── README.md             # 项目文档
```

### 扩展开发

1. **添加新的分析引擎**
```python
# core/new_engine.py
class NewAnalysisEngine:
    def __init__(self, llm_interface):
        self.llm = llm_interface
    
    async def analyze(self, data):
        # 实现分析逻辑
        pass
```

2. **添加新的搜索源**
```python
# 在 ecosystem_scanner.py 中扩展
async def _search_new_source(self, query):
    # 实现新搜索源的逻辑
    pass
```

3. **自定义推荐策略**
```python
# 在 decision_engine.py 中添加
def _custom_recommendation_strategy(self, data):
    # 实现自定义推荐逻辑
    pass
```

## API 文档

### 核心分析接口

```python
# 需求分析
result = await app.analyze_demand(
    user_input="用户需求描述",
    user_mode="regular",  # 或 "developer"
    language="zh"         # 或 "en"
)
```

### 响应格式

```json
{
  "demand_analysis": {
    "intent": "用户意图",
    "category": "需求分类",
    "keywords": ["关键词"],
    "summary": "需求摘要"
  },
  "ecosystem_scan": {
    "competitors": [...],
    "market_maturity": 0.8
  },
  "recommendations": [...],
  "feasibility_score": 0.85,
  "confidence_level": 0.92
}
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档: [项目Wiki]

## 致谢

- [Gradio](https://gradio.app) - 优秀的机器学习界面框架
- [OpenAI](https://openai.com) - 强大的语言模型API
- [Tavily](https://tavily.com) - 高质量的搜索API服务
- 所有贡献者和用户的支持

---

**逛逛 (InsightPulse)** - 让每个想法都能找到最佳的实现路径 🚀
