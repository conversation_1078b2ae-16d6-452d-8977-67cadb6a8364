{"llm": {"providers": {"deepseek": {"models": {"reasoning": "deepseek-reasoner", "analysis": "deepseek-chat", "summary": "deepseek-chat"}}, "openai": {"models": {"reasoning": "gpt-4o", "analysis": "gpt-4o", "summary": "gpt-4o"}}, "claude": {"models": {"reasoning": "claude-sonnet-4-20250514", "analysis": "claude-sonnet-4-20250514", "summary": "claude-sonnet-4-20250514"}}, "openai_compatible": {"models": {"reasoning": "gpt-4", "analysis": "gpt-4", "summary": "gpt-3.5-turbo"}}, "openai_compatible_2": {"models": {"reasoning": "gpt-4", "analysis": "gpt-4", "summary": "gpt-3.5-turbo"}}}, "temperature": 0.6, "max_tokens": 4000}, "search": {"tavily_api_key": "", "max_results": 10, "search_depth": "advanced"}, "app": {"debug": false, "log_level": "INFO", "max_history": 100, "cache_enabled": true, "cache_ttl": 3600}, "ui": {"default_language": "zh", "theme": "soft", "max_chat_history": 50}, "analysis": {"max_competitors": 10, "max_search_queries": 5, "confidence_threshold": 0.7}}