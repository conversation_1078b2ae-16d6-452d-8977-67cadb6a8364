#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Startup script for InsightPulse application
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required packages are installed"""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'gradio',
        'openai',
        'tavily-python',
        'pandas',
        'numpy',
        'requests'
    ]
    
    missing = []
    for package in required_packages:
        try:
            if package == 'tavily-python':
                import tavily
            else:
                __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  Missing packages: {', '.join(missing)}")
        print("Installing missing packages...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install -r requirements.txt")
            return False
    
    return True

def check_environment():
    """Check environment variables"""
    print("\n🔑 Checking environment variables...")

    # 检查所有可能的API密钥
    llm_providers = {
        'DEEPSEEK_API_KEY': 'DeepSeek API',
        'CLAUDE_API_KEY': 'Claude API',
        'OPENAI_API_KEY': 'OpenAI API',
        'OPENAI_COMPATIBLE_API_KEY': 'OpenAI Compatible API'
    }

    search_providers = {
        'TAVILY_API_KEY': 'Tavily Search API'
    }

    # 检查LLM提供商
    available_llm = []
    for var, description in llm_providers.items():
        if os.getenv(var):
            print(f"✅ {var} ({description})")
            available_llm.append(description)
        else:
            print(f"⚪ {var} ({description}) - NOT SET")

    # 检查搜索提供商
    available_search = []
    for var, description in search_providers.items():
        if os.getenv(var):
            print(f"✅ {var} ({description})")
            available_search.append(description)
        else:
            print(f"⚪ {var} ({description}) - NOT SET")

    # 显示状态
    if available_llm:
        print(f"\n✅ 可用的LLM服务: {', '.join(available_llm)}")
    else:
        print(f"\n⚠️  未配置任何LLM API密钥，AI功能将不可用")

    if available_search:
        print(f"✅ 可用的搜索服务: {', '.join(available_search)}")
    else:
        print(f"⚠️  未配置搜索API密钥，搜索功能将不可用")

    if not available_llm and not available_search:
        print(f"\n💡 提示：虽然未配置API密钥，但您仍可以启动应用查看界面")
        print("配置方法：")
        print("1. 编辑 .env 文件")
        print("2. 设置系统环境变量")
        print("3. 在shell中export变量")

    return True  # 总是返回True，允许无密钥启动

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = ['logs', 'locales']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}/")
    
    return True

def load_environment():
    """Load environment variables from .env file"""
    env_file = Path('.env')
    if env_file.exists():
        print("📄 Loading .env file...")
        try:
            from dotenv import load_dotenv
            load_dotenv()
            print("✅ Environment variables loaded")
        except ImportError:
            print("⚠️  python-dotenv not installed, loading .env manually...")
            with open('.env', 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
            print("✅ Environment variables loaded manually")
    else:
        print("⚠️  No .env file found")

def run_application():
    """Run the main application"""
    print("\n🚀 Starting InsightPulse application...")
    print("=" * 50)
    print("\n💡 网络提示:")
    print("- 如果浏览器控制台出现CORS或网络错误，可以忽略")
    print("- 这些错误不影响应用的核心功能")
    print("- 应用的AI分析、搜索等功能都是独立运行的")
    print("=" * 50)

    try:
        # Import and run the main app
        from app import main
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Application error: {str(e)}")
        print("\nFor help, please check:")
        print("1. README.md for setup instructions")
        print("2. Run 'python test_app.py' to diagnose issues")
        print("3. NETWORK_TROUBLESHOOTING.md for network issues")
        return False

    return True

def main():
    """Main startup function"""
    print("🔍 逛逛 (InsightPulse) - 需求探索与可行性分析平台")
    print("=" * 60)
    
    # Check system requirements
    if not check_python_version():
        return False
    
    # Load environment variables
    load_environment()
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    # Check environment variables
    if not check_environment():
        return False
    
    # Create directories
    if not create_directories():
        return False
    
    print("\n✅ All checks passed!")
    
    # Run application
    return run_application()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
