# 模型选择功能指南

## 🎯 功能概述

逛逛 (InsightPulse) 现在支持多AI提供商和实时模型切换功能，让您可以根据需要选择最适合的AI模型进行分析。

## 🔧 界面功能

### 1. 模型选择器
- **位置**: 界面顶部，语言选择器旁边
- **功能**: 实时切换AI提供商
- **显示**: 当前可用的所有AI服务

### 2. 系统状态面板
- **位置**: 界面右侧
- **显示内容**:
  - 🤖 AI功能状态
  - 当前使用的提供商
  - 可用的提供商列表
  - 🔍 搜索功能状态

### 3. 模型配置详情
- **位置**: 系统状态面板下方
- **显示内容**:
  - 当前提供商名称
  - 推理模型名称
  - 分析模型名称
  - 摘要模型名称

### 4. 分析结果标识
- **位置**: 分析结果底部
- **显示**: 使用的AI模型信息

## 🚀 使用方法

### 基本使用
1. **启动应用**: 运行 `python start.py`
2. **查看状态**: 检查右侧系统状态面板
3. **选择模型**: 在模型选择器中选择想要的AI提供商
4. **开始分析**: 输入需求进行分析

### 模型切换
1. **实时切换**: 在分析过程中可随时切换模型
2. **状态更新**: 切换后系统状态和模型详情会自动更新
3. **下次分析**: 新的分析将使用选择的模型

## 📊 支持的AI提供商

### 1. DeepSeek (推荐)
- **特点**: 性价比高，中文支持好，推理能力强
- **适用场景**: 日常分析、中文内容处理
- **模型**: DeepSeek-R1, Qwen2.5-72B

### 2. OpenAI (官方)
- **特点**: 功能强大，生态完善
- **适用场景**: 复杂分析、英文内容处理
- **模型**: GPT-4, GPT-3.5-turbo

### 3. Claude (Anthropic)
- **特点**: 安全性高，长文本处理能力强
- **适用场景**: 安全敏感场景、长文档分析
- **模型**: Claude-3, Claude-2

### 4. OpenAI兼容服务
- **特点**: 第三方服务，通常更便宜
- **适用场景**: 成本敏感场景
- **模型**: 各种兼容模型

## ⚙️ 配置示例

### 多提供商配置
```bash
# .env 文件配置
DEEPSEEK_API_KEY=sk-your-deepseek-key
OPENAI_API_KEY=sk-your-openai-key
CLAUDE_API_KEY=sk-ant-your-claude-key
TAVILY_API_KEY=tvly-your-tavily-key
```

### 单提供商配置
```bash
# 只配置一个即可
DEEPSEEK_API_KEY=sk-your-deepseek-key
TAVILY_API_KEY=tvly-your-tavily-key
```

## 🎨 界面状态说明

### 系统状态指示器
- **✅ 绿色**: 功能正常可用
- **⚠️ 橙色**: 功能不可用或未配置
- **❌ 红色**: 功能错误

### 模型选择器状态
- **有选项**: 显示可用的AI提供商
- **"未配置AI服务"**: 没有可用的API密钥

### 配置提示
- **黄色提示框**: 提醒配置API密钥
- **绿色详情框**: 显示当前模型配置
- **橙色警告框**: 提示配置问题

## 💡 使用建议

### 模型选择策略
1. **日常使用**: DeepSeek (性价比最高)
2. **专业分析**: OpenAI (功能最全)
3. **安全场景**: Claude (安全性最高)
4. **成本优化**: OpenAI兼容服务

### 切换时机
- **分析前**: 根据需求类型选择合适的模型
- **分析中**: 如果结果不满意可切换模型重试
- **对比分析**: 使用不同模型分析同一需求进行对比

### 性能优化
- **网络延迟**: 选择地理位置较近的服务商
- **响应速度**: DeepSeek通常响应最快
- **准确性**: OpenAI在复杂任务上表现更好

## 🔍 故障排除

### 模型选择器显示"未配置AI服务"
- **原因**: 没有配置任何有效的API密钥
- **解决**: 在.env文件中配置至少一个API密钥

### 切换模型后没有反应
- **原因**: 新模型的API密钥可能无效
- **解决**: 检查API密钥是否正确配置

### 分析结果显示错误信息
- **原因**: 当前模型不可用或API配额不足
- **解决**: 切换到其他可用的模型

### 系统状态显示不准确
- **原因**: 界面缓存问题
- **解决**: 刷新页面或重启应用

## 📈 功能优势

### 1. 灵活性
- 支持多个AI提供商
- 实时切换无需重启
- 根据需求选择最适合的模型

### 2. 可靠性
- 多提供商备份
- 自动故障转移
- 优雅的错误处理

### 3. 透明性
- 清晰的状态显示
- 详细的配置信息
- 分析结果标识使用的模型

### 4. 成本控制
- 根据预算选择提供商
- 避免单一供应商依赖
- 优化使用成本

---

**享受多模型带来的灵活性和可靠性！** 🎉
