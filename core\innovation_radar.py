#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Innovation Radar Module
创新雷达与可行性评估模块 - 负责评估创新机会和技术可行性
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class InnovationOpportunity:
    """创新机会"""
    opportunity_type: str
    description: str
    market_size: str
    competition_level: str
    technical_difficulty: str
    time_to_market: str
    investment_required: str
    success_probability: float

class InnovationRadar:
    """创新雷达与可行性评估模块"""
    
    def __init__(self, llm_interface):
        """初始化创新雷达"""
        self.llm = llm_interface
        
        # 评估维度权重
        self.assessment_weights = {
            "market_opportunity": 0.3,
            "technical_feasibility": 0.25,
            "competitive_landscape": 0.2,
            "resource_requirements": 0.15,
            "risk_factors": 0.1
        }
        
        logger.info("创新雷达初始化完成")
    
    async def assess(
        self,
        demand_analysis: Dict[str, Any],
        ecosystem_scan: Dict[str, Any],
        user_mode: str
    ) -> Dict[str, Any]:
        """
        执行创新雷达分析
        
        Args:
            demand_analysis: 需求分析结果
            ecosystem_scan: 生态扫描结果
            user_mode: 用户模式 (regular/developer)
            
        Returns:
            创新评估结果字典
        """
        try:
            logger.info("开始创新雷达分析...")
            
            # 市场机会评估
            market_opportunity = await self._assess_market_opportunity(
                demand_analysis, ecosystem_scan
            )
            
            # 技术可行性评估
            technical_feasibility = await self._assess_technical_feasibility(
                demand_analysis, ecosystem_scan
            )
            
            # 竞争格局分析
            competitive_analysis = await self._analyze_competitive_landscape(
                ecosystem_scan
            )
            
            # 资源需求评估
            resource_requirements = await self._assess_resource_requirements(
                demand_analysis, technical_feasibility
            )
            
            # 风险因素分析
            risk_factors = await self._analyze_risk_factors(
                demand_analysis, ecosystem_scan, technical_feasibility
            )
            
            # 历史案例分析
            historical_cases = await self._analyze_historical_cases(
                demand_analysis, ecosystem_scan
            )
            
            # 综合评分计算
            overall_score = self._calculate_overall_score({
                "market_opportunity": market_opportunity,
                "technical_feasibility": technical_feasibility,
                "competitive_analysis": competitive_analysis,
                "resource_requirements": resource_requirements,
                "risk_factors": risk_factors
            })
            
            # 构建评估结果
            assessment_result = {
                "market_opportunity": market_opportunity,
                "technical_feasibility": technical_feasibility,
                "competitive_analysis": competitive_analysis,
                "resource_requirements": resource_requirements,
                "risk_factors": risk_factors,
                "historical_cases": historical_cases,
                "overall_score": overall_score,
                "assessment_timestamp": self._get_timestamp(),
                "user_mode": user_mode
            }
            
            logger.info(f"创新雷达分析完成，综合评分: {overall_score:.2f}")
            return assessment_result
            
        except Exception as e:
            logger.error(f"创新雷达分析失败: {str(e)}")
            return self._create_fallback_assessment(demand_analysis, ecosystem_scan)
    
    async def _assess_market_opportunity(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估市场机会"""
        try:
            prompt = self._build_market_opportunity_prompt(demand_analysis, ecosystem_scan)
            response = await self.llm.generate_analysis(prompt)
            result = await self.llm.parse_json_response(response.content)
            
            return {
                "level": result.get("opportunity_level", "中等"),
                "market_size": result.get("market_size", "未知"),
                "growth_potential": result.get("growth_potential", "中等"),
                "target_segments": result.get("target_segments", []),
                "gap_analysis": result.get("gap_analysis", "需要进一步分析"),
                "monetization_potential": result.get("monetization_potential", "中等"),
                "score": float(result.get("score", 0.5))
            }
            
        except Exception as e:
            logger.warning(f"市场机会评估失败: {str(e)}")
            return {
                "level": "中等",
                "market_size": "未知",
                "growth_potential": "中等",
                "target_segments": [],
                "gap_analysis": "需要进一步分析",
                "monetization_potential": "中等",
                "score": 0.5
            }
    
    async def _assess_technical_feasibility(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估技术可行性"""
        try:
            prompt = self._build_technical_feasibility_prompt(demand_analysis, ecosystem_scan)
            response = await self.llm.generate_analysis(prompt)
            result = await self.llm.parse_json_response(response.content)
            
            return {
                "difficulty": result.get("difficulty_level", "中等"),
                "tech_stack": result.get("required_tech_stack", []),
                "timeline": result.get("development_timeline", "待评估"),
                "team_size": result.get("required_team_size", "待评估"),
                "key_challenges": result.get("key_challenges", []),
                "existing_solutions": result.get("existing_solutions", []),
                "innovation_level": result.get("innovation_level", "中等"),
                "score": float(result.get("score", 0.5))
            }
            
        except Exception as e:
            logger.warning(f"技术可行性评估失败: {str(e)}")
            return {
                "difficulty": "中等",
                "tech_stack": [],
                "timeline": "待评估",
                "team_size": "待评估",
                "key_challenges": [],
                "existing_solutions": [],
                "innovation_level": "中等",
                "score": 0.5
            }
    
    async def _analyze_competitive_landscape(
        self, 
        ecosystem_scan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析竞争格局"""
        competitors = ecosystem_scan.get("competitors", [])
        market_maturity = ecosystem_scan.get("market_maturity", 0.5)
        
        # 竞争强度分析
        if len(competitors) >= 10:
            competition_intensity = "高"
            intensity_score = 0.2
        elif len(competitors) >= 5:
            competition_intensity = "中"
            intensity_score = 0.5
        elif len(competitors) >= 2:
            competition_intensity = "低"
            intensity_score = 0.8
        else:
            competition_intensity = "极低"
            intensity_score = 0.9
        
        # 市场领导者分析
        market_leaders = []
        for comp in competitors[:3]:
            if comp.get("rating", 0) > 4.0 or "领导" in comp.get("market_position", ""):
                market_leaders.append(comp.get("name", ""))
        
        # 进入壁垒分析
        if market_maturity > 0.8:
            entry_barriers = "高"
        elif market_maturity > 0.5:
            entry_barriers = "中"
        else:
            entry_barriers = "低"
        
        return {
            "competition_intensity": competition_intensity,
            "intensity_score": intensity_score,
            "market_leaders": market_leaders,
            "entry_barriers": entry_barriers,
            "differentiation_opportunities": self._identify_differentiation_opportunities(competitors),
            "competitive_advantages": [],
            "score": intensity_score
        }
    
    async def _assess_resource_requirements(
        self, 
        demand_analysis: Dict[str, Any], 
        technical_feasibility: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估资源需求"""
        
        # 基于技术难度估算资源需求
        difficulty = technical_feasibility.get("difficulty", "中等")
        
        if difficulty == "高":
            funding_requirement = "高（>100万）"
            team_size = "大型团队（10+人）"
            timeline = "长期（12+月）"
            score = 0.3
        elif difficulty == "中":
            funding_requirement = "中等（10-100万）"
            team_size = "中型团队（5-10人）"
            timeline = "中期（6-12月）"
            score = 0.6
        else:
            funding_requirement = "低（<10万）"
            team_size = "小型团队（2-5人）"
            timeline = "短期（3-6月）"
            score = 0.9
        
        return {
            "funding_requirement": funding_requirement,
            "team_size": team_size,
            "timeline": timeline,
            "key_skills": technical_feasibility.get("tech_stack", []),
            "infrastructure_needs": [],
            "score": score
        }
    
    async def _analyze_risk_factors(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any], 
        technical_feasibility: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析风险因素"""
        
        risks = []
        risk_score = 0.7  # 基础风险评分
        
        # 市场风险
        if ecosystem_scan.get("market_maturity", 0) > 0.8:
            risks.append("市场饱和风险")
            risk_score -= 0.1
        
        # 技术风险
        if technical_feasibility.get("difficulty", "中等") == "高":
            risks.append("技术实现风险")
            risk_score -= 0.2
        
        # 竞争风险
        if len(ecosystem_scan.get("competitors", [])) > 10:
            risks.append("激烈竞争风险")
            risk_score -= 0.1
        
        # 资源风险
        if "高" in technical_feasibility.get("timeline", ""):
            risks.append("资源投入风险")
            risk_score -= 0.1
        
        return {
            "identified_risks": risks,
            "risk_level": "高" if risk_score < 0.4 else "中" if risk_score < 0.7 else "低",
            "mitigation_strategies": [],
            "score": max(0.1, risk_score)
        }
    
    async def _analyze_historical_cases(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """分析历史案例"""
        # 这里可以集成历史案例数据库
        # 目前返回模拟数据
        return [
            {
                "title": "相似需求的成功案例",
                "description": "基于搜索结果的相关案例分析",
                "success_factors": ["市场时机", "产品定位", "团队执行"],
                "lessons_learned": ["用户需求验证的重要性"],
                "relevance": 0.7,
                "url": ""
            }
        ]
    
    def _build_market_opportunity_prompt(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> str:
        """构建市场机会评估提示词"""
        
        competitors_summary = ""
        for comp in ecosystem_scan.get("competitors", [])[:5]:
            competitors_summary += f"- {comp.get('name', '')}: {comp.get('description', '')}\n"
        
        return f"""作为市场分析专家，请评估以下需求的市场机会：

用户需求：{demand_analysis.get('summary', '')}
需求类别：{demand_analysis.get('category', '')}
关键词：{', '.join(demand_analysis.get('keywords', []))}

现有竞品：
{competitors_summary}

市场成熟度：{ecosystem_scan.get('market_maturity', 0.5)}

请按JSON格式返回市场机会评估：

{{
    "opportunity_level": "高/中/低",
    "market_size": "市场规模估算",
    "growth_potential": "增长潜力",
    "target_segments": ["目标用户群体"],
    "gap_analysis": "市场空白分析",
    "monetization_potential": "变现潜力",
    "score": 0.8
}}"""
    
    def _build_technical_feasibility_prompt(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> str:
        """构建技术可行性评估提示词"""
        
        return f"""作为技术专家，请评估以下需求的技术可行性：

用户需求：{demand_analysis.get('summary', '')}
功能特性：{', '.join(demand_analysis.get('priority_features', []))}

请按JSON格式返回技术可行性评估：

{{
    "difficulty_level": "高/中/低",
    "required_tech_stack": ["所需技术栈"],
    "development_timeline": "开发周期估算",
    "required_team_size": "团队规模需求",
    "key_challenges": ["主要技术挑战"],
    "existing_solutions": ["可复用的现有方案"],
    "innovation_level": "创新程度",
    "score": 0.7
}}"""
    
    def _identify_differentiation_opportunities(self, competitors: List[Dict[str, Any]]) -> List[str]:
        """识别差异化机会"""
        opportunities = []
        
        if len(competitors) < 3:
            opportunities.append("市场空白机会")
        
        # 分析竞品功能，寻找差异化点
        all_features = []
        for comp in competitors:
            all_features.extend(comp.get("features", []))
        
        if not all_features:
            opportunities.append("功能创新机会")
        
        return opportunities[:3]
    
    def _calculate_overall_score(self, assessments: Dict[str, Any]) -> float:
        """计算综合评分"""
        total_score = 0.0
        
        for dimension, weight in self.assessment_weights.items():
            if dimension in assessments:
                score = assessments[dimension].get("score", 0.5)
                total_score += score * weight
        
        return min(1.0, max(0.0, total_score))
    
    def _create_fallback_assessment(
        self, 
        demand_analysis: Dict[str, Any], 
        ecosystem_scan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建备用评估结果"""
        return {
            "market_opportunity": {"level": "中等", "score": 0.5},
            "technical_feasibility": {"difficulty": "中等", "score": 0.5},
            "competitive_analysis": {"competition_intensity": "中等", "score": 0.5},
            "resource_requirements": {"funding_requirement": "中等", "score": 0.5},
            "risk_factors": {"risk_level": "中等", "score": 0.5},
            "historical_cases": [],
            "overall_score": 0.5,
            "assessment_timestamp": self._get_timestamp(),
            "fallback": True
        }
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
